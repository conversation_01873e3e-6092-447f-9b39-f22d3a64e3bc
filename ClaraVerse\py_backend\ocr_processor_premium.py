"""
Premium OCR Processor for ClaraVerse
Based on the excellent OCR engine from Assistant I<PERSON><PERSON> project
Optimized for financial documents, legal documents, and complex layouts
"""

import os
import tempfile
import subprocess
import time
import re
import pytesseract
from pdf2image import convert_from_path
from PIL import Image, ImageEnhance, ImageFilter, ImageOps
from pathlib import Path
import logging
from typing import List, Optional, Union, Dict, Tuple
import cv2
import numpy as np
import fitz  # PyMuPDF pour extraction avancée PDF
from pdf2image.exceptions import PDFPageCountError
import time

# Imports pour formats Office (installation optionnelle)
try:
    from docx import Document  # python-docx
    DOCX_AVAILABLE = True
except ImportError:
    DOCX_AVAILABLE = False

try:
    from pptx import Presentation  # python-pptx
    PPTX_AVAILABLE = True
except ImportError:
    PPTX_AVAILABLE = False

# Configuration du logger
logger = logging.getLogger(__name__)

class PremiumOcrProcessor:
    """
    Processeur OCR Premium pour documents professionnels complexes.
    Optimisé spécialement pour :
    - Comptes sociaux avec tableaux
    - Documents juridiques 
    - PDF scannés de mauvaise qualité
    - Tableaux et structures complexes
    """

    def __init__(self):
        # Configuration OPTIMISÉE pour la fiabilité
        self.language = "fra"  # Langue française
        self.dpi = 200  # DPI réduit pour éviter les problèmes (300 était trop élevé)
        self.tesseract_path = self._find_tesseract_path()
        self.poppler_path = os.getenv("POPPLER_PATH")  # Ajout manquant
        self.temp_dir = tempfile.gettempdir()
        self.preprocess_images = True  # EXACTEMENT comme Rust

        # Paramètres de prétraitement (AJOUT MANQUANT)
        self.deskew = True  # Correction d'inclinaison
        self.enhance_contrast = True  # Amélioration du contraste
        self.detect_tables = True  # Détection de tableaux
        self.confidence_threshold = 30  # Seuil de confiance minimum

        # Paramètres Tesseract EXACTS du Rust
        self.psm_mode = 4  # EXACTEMENT comme Rust (pas 6)
        self.oem_mode = 3  # EXACTEMENT comme Rust

        # Whitelist EXACTE du Rust + caractères français accentués
        self.char_whitelist = "ABCDEFGHIJKLMNOPQRSTUVWXYZabcdefghijklmnopqrstuvwxyz0123456789.,€$%:;-_/\\()[]{}@#&*+=<>?!\"'àáâãäåæçèéêëìíîïðñòóôõöøùúûüýþÿÀÁÂÃÄÅÆÇÈÉÊËÌÍÎÏÐÑÒÓÔÕÖØÙÚÛÜÝÞß"
        
        # Configuration Tesseract EXACTE du Rust
        self._configure_tesseract()

        logger.info("🔧 Processeur OCR Premium initialisé (COPIE EXACTE Assistant Impot)")
        logger.info(f"   📊 DPI: {self.dpi} (EXACTEMENT comme Rust)")
        logger.info(f"   🌍 Langue: {self.language} (EXACTEMENT comme Rust)")
        logger.info(f"   🎯 Mode PSM: {self.psm_mode} (EXACTEMENT comme Rust)")
        logger.info(f"   ✨ Prétraitement: {self.preprocess_images} (EXACTEMENT comme Rust)")

    def _find_tesseract_path(self):
        """Rechercher Tesseract EXACTEMENT comme dans le Rust (lignes 47-66)"""
        possible_paths = [
            "C:\\Program Files\\Tesseract-OCR\\tesseract.exe",
            "C:\\Program Files (x86)\\Tesseract-OCR\\tesseract.exe",
            "C:\\vcpkg\\installed\\x64-windows\\bin\\tesseract.exe",
            "C:\\vcpkg\\installed\\x86-windows\\bin\\tesseract.exe",
            "tesseract.exe",
            "tesseract"
        ]

        tesseract_path = "tesseract"  # Valeur par défaut EXACTE du Rust

        logger.info("Recherche de Tesseract OCR...")
        for path in possible_paths:
            if os.path.exists(path):
                logger.info(f"Tesseract trouvé à: {path}")
                tesseract_path = path
                break

        return tesseract_path

    def _configure_tesseract(self):
        """Configuration EXACTE du Rust (lignes 68-86)"""
        # Vérifier que Tesseract est disponible EXACTEMENT comme Rust
        try:
            result = subprocess.run([self.tesseract_path, "--version"],
                                  capture_output=True, text=True, check=False)
            if result.returncode == 0:
                logger.info(f"Tesseract est disponible: {result.stdout.strip()}")
            else:
                logger.warning(f"Tesseract a retourné une erreur: {result.stderr.strip()}")
        except Exception as e:
            logger.warning(f"ATTENTION: Tesseract n'est pas disponible: {e}. L'OCR ne fonctionnera pas correctement.")
            logger.warning("Veuillez installer Tesseract OCR et vous assurer qu'il est dans le PATH.")
            logger.warning("Téléchargement: https://github.com/UB-Mannheim/tesseract/wiki")

        # Configurer pytesseract
        if self.tesseract_path != "tesseract":
            pytesseract.pytesseract.tesseract_cmd = self.tesseract_path

        logger.info(f"Utilisation du répertoire temporaire: {self.temp_dir}")

    def process_document(self, file_path: str) -> Dict[str, any]:
        """
        Traiter un document avec OCR premium et retourner des informations détaillées
        Compatible avec l'interface ClaraVerse
        """
        start_time = time.time()
        path = Path(file_path)
        
        if not path.exists():
            raise FileNotFoundError(f"Fichier non trouvé: {file_path}")
        
        extension = path.suffix.lower()
        logger.info(f"🔍 Traitement document: {path.name} ({extension})")
        
        # Vérifier la taille
        file_size = path.stat().st_size
        max_size = self._get_max_file_size()
        if file_size > max_size:
            raise ValueError(f"Fichier trop volumineux: {file_size/1024/1024:.1f}MB > {max_size/1024/1024:.1f}MB")
        
        result = {
            "success": True,
            "text": "",
            "formatted_text": "",
            "confidence": 0,
            "detected_language": "",
            "pages_count": 0,
            "tables_detected": [],
            "images_extracted": 0,
            "method_used": "",
            "quality_score": 0,
            "processing_time": 0,
            "file_name": path.name,
            "file_size": file_size,
            "extension": extension,
            "error_message": None
        }
        
        try:
            if extension == ".txt":
                result.update(self._process_text_file(file_path))
            elif extension == ".pdf":
                result.update(self._process_pdf_premium(file_path))
            elif extension in [".jpg", ".jpeg", ".png", ".tiff", ".bmp"]:
                result.update(self._process_image_premium(file_path))
            elif extension == ".docx":
                result.update(self._process_word_document(file_path))
            elif extension == ".pptx":
                result.update(self._process_powerpoint_document(file_path))
            else:
                supported = [".txt", ".pdf", ".jpg", ".jpeg", ".png", ".tiff", ".bmp"]
                if DOCX_AVAILABLE:
                    supported.append(".docx")
                if PPTX_AVAILABLE:
                    supported.append(".pptx")
                raise ValueError(f"Format non supporté: {extension}. Supportés: {', '.join(supported)}")
            
            result["processing_time"] = time.time() - start_time
            
            # Score de qualité basé sur plusieurs facteurs
            result["quality_score"] = self._calculate_quality_score(result)
            
            # Formatted text = text pour compatibilité ClaraVerse
            if not result.get("formatted_text"):
                result["formatted_text"] = result["text"]
            
            logger.info(f"✅ Document traité en {result['processing_time']:.1f}s")
            logger.info(f"   📝 Texte: {len(result['text'])} caractères")
            logger.info(f"   🎯 Confiance: {result['confidence']:.1f}%")
            logger.info(f"   ⭐ Qualité: {result['quality_score']:.1f}/100")
            
            return result
            
        except Exception as e:
            logger.error(f"❌ Erreur traitement {path.name}: {e}")
            result.update({
                "success": False,
                "error_message": str(e),
                "processing_time": time.time() - start_time
            })
            return result

    def _process_pdf_premium(self, pdf_path: str) -> Dict[str, any]:
        """Traitement PDF EXACTEMENT comme Rust avec option force OCR"""
        result = {"method_used": "pdf_premium", "pages_count": 0}

        # Option pour forcer l'OCR (résout les problèmes d'encodage PDF)
        force_ocr = os.getenv("FORCE_OCR", "false").lower() == "true"

        if not force_ocr:
            # ÉTAPE 1: Essayer pdftotext d'abord EXACTEMENT comme Rust (lignes 125-141)
            try:
                result_pdftotext = subprocess.run([
                    "pdftotext",
                    "-layout",  # Maintenir la mise en page originale
                    pdf_path,   # PDF d'entrée
                    "-"         # Sortie vers stdout
                ], capture_output=True, text=True, check=False)

                if result_pdftotext.returncode == 0:
                    text = result_pdftotext.stdout
                    if text.strip():  # Si le texte n'est pas vide
                        # Appliquer les corrections d'encodage même sur pdftotext
                        text = self._fix_encoding_issues(text)

                        # Vérifier si le texte a encore des problèmes d'encodage
                        encoding_problems = text.count('Ã') + text.count('â€')

                        if encoding_problems == 0:
                            logger.info("⚡ Extraction directe pdftotext réussie (encodage correct)")
                            result["text"] = text
                            result["method_used"] = "pdf_direct_pdftotext_fixed"
                            result["pages_count"] = 1
                            return result
                        else:
                            logger.warning(f"⚠️ pdftotext a encore {encoding_problems} problèmes d'encodage, passage à l'OCR")
            except FileNotFoundError:
                logger.info("pdftotext non disponible, passage à l'OCR")
            except Exception as e:
                logger.info(f"pdftotext échoué: {e}, passage à l'OCR")
        else:
            logger.info("🔧 Mode FORCE_OCR activé, passage direct à l'OCR")

        # ÉTAPE 2: OCR sur images EXACTEMENT comme Rust (lignes 143-191)
        logger.info("📸 Extraction par OCR nécessaire (PDF scanné détecté ou force_ocr)")
        text_ocr, ocr_info = self._extract_pdf_with_ocr_premium(pdf_path)
        result.update(ocr_info)
        result["text"] = text_ocr
        result["method_used"] = "pdf_ocr_premium"

        return result

    def _extract_pdf_text_direct(self, pdf_path: str) -> str:
        """Extraction directe du texte PDF avec PyMuPDF"""
        try:
            doc = fitz.open(pdf_path)
            text_parts = []
            
            for page_num in range(doc.page_count):
                page = doc[page_num]
                
                # Extraction avec préservation de la structure avancée
                text = page.get_text("text")

                if self.detect_tables:
                    # Essayer d'extraire les tableaux structurés avec PyMuPDF
                    try:
                        tables = page.find_tables()
                        if tables:
                            logger.info(f"📊 {len(tables)} tableaux détectés page {page_num + 1}")

                            # Remplacer le texte par une version avec tableaux formatés
                            text_with_tables = self._integrate_tables_in_text(page, tables)
                            if text_with_tables:
                                text = text_with_tables
                            else:
                                # Fallback : ajouter les tableaux à la fin
                                for i, table in enumerate(tables):
                                    try:
                                        table_data = table.extract()
                                        if table_data and len(table_data) > 0:
                                            table_markdown = self._format_table_text(table_data)
                                            text += f"\n\n## Tableau {i+1}\n\n{table_markdown}\n\n"
                                    except Exception as e:
                                        logger.warning(f"Erreur extraction tableau {i+1}: {e}")
                    except Exception as e:
                        logger.warning(f"Erreur détection tableaux page {page_num + 1}: {e}")

                # Améliorer la structure générale du texte
                text = self._improve_text_structure(text)
                
                text_parts.append(text)
            
            doc.close()
            return "\n\n".join(text_parts)
            
        except Exception as e:
            logger.warning(f"⚠️  Extraction directe PDF échouée: {e}")
            return ""

    def _get_max_file_size(self) -> int:
        """Récupérer la taille maximale autorisée"""
        max_size_env = os.getenv("MAX_FILE_SIZE", "500MB")
        if max_size_env.endswith("MB"):
            return int(max_size_env[:-2]) * 1024 * 1024
        elif max_size_env.endswith("GB"):
            return int(max_size_env[:-2]) * 1024 * 1024 * 1024
        else:
            return 500 * 1024 * 1024  # Par défaut 500MB

    def _extract_pdf_with_ocr_premium(self, pdf_path: str) -> Tuple[str, Dict]:
        """Extraction PDF avec OCR premium optimisé pour gros PDF"""
        try:
            # Configuration Poppler
            poppler_kwargs = {}
            if self.poppler_path:
                poppler_kwargs["poppler_path"] = self.poppler_path

            # Vérifier d'abord le nombre de pages
            logger.info(f"🔍 Analyse du PDF: {pdf_path}")

            # Conversion par petits lots pour les gros PDF
            logger.info(f"🔄 Conversion PDF → Images (DPI {self.dpi})")

            # Convertir d'abord une page pour estimer la taille
            first_page_images = convert_from_path(
                pdf_path,
                dpi=self.dpi,
                first_page=1,
                last_page=1,
                fmt='ppm',
                **poppler_kwargs
            )

            if not first_page_images:
                raise RuntimeError("Impossible de convertir la première page")

            # Estimer le nombre total de pages
            try:
                all_images = convert_from_path(
                    pdf_path,
                    dpi=self.dpi,
                    first_page=None,
                    last_page=None,
                    fmt='ppm',
                    thread_count=1,  # Réduire pour éviter les problèmes de mémoire
                    **poppler_kwargs
                )
                total_pages = len(all_images)
                logger.info(f"📊 PDF contient {total_pages} pages")

                # Traitement par lots pour les gros PDF
                if total_pages > 20:
                    logger.info(f"🔄 Traitement par lots (PDF volumineux: {total_pages} pages)")
                    return self._process_large_pdf_in_batches(pdf_path, total_pages, poppler_kwargs)
                else:
                    images = all_images

            except Exception as e:
                logger.warning(f"⚠️ Impossible d'estimer la taille du PDF: {e}")
                # Fallback: traiter page par page
                return self._process_pdf_page_by_page(pdf_path, poppler_kwargs)

            text_parts = []
            total_confidence = 0
            tables_detected = []

            for i, image in enumerate(images):
                try:
                    logger.info(f"📄 Traitement page {i + 1}/{len(images)} ({((i+1)/len(images)*100):.1f}%)")

                    # Prétraitement premium de l'image
                    processed_image = self._preprocess_image_premium(image)

                    # OCR avec configuration premium
                    page_result = self._ocr_image_premium(processed_image)

                    text_parts.append(page_result["text"])
                    total_confidence += page_result["confidence"]

                    if page_result.get("tables"):
                        tables_detected.extend(page_result["tables"])

                    # Libérer la mémoire
                    del image
                    del processed_image

                except Exception as e:
                    logger.error(f"❌ Erreur page {i + 1}: {e}")
                    text_parts.append(f"[ERREUR PAGE {i + 1}: {str(e)}]")

            avg_confidence = total_confidence / len(images) if images else 0

            logger.info(f"✅ OCR terminé: {len(images)} pages traitées")

            return "\n\n".join(text_parts), {
                "pages_count": len(images),
                "confidence": avg_confidence,
                "tables_detected": tables_detected,
                "images_extracted": len(images)
            }

        except Exception as e:
            logger.error(f"❌ OCR PDF premium échoué: {e}")
            raise RuntimeError(f"Erreur OCR PDF: {e}")

    def _process_large_pdf_in_batches(self, pdf_path: str, total_pages: int, poppler_kwargs: dict) -> Tuple[str, Dict]:
        """Traiter un gros PDF par lots pour éviter les problèmes de mémoire"""
        batch_size = 10  # Traiter 10 pages à la fois
        text_parts = []
        total_confidence = 0
        tables_detected = []
        processed_pages = 0

        logger.info(f"🔄 Traitement par lots de {batch_size} pages")

        for start_page in range(1, total_pages + 1, batch_size):
            end_page = min(start_page + batch_size - 1, total_pages)

            try:
                logger.info(f"📦 Lot {start_page}-{end_page} sur {total_pages} pages")

                # Convertir le lot
                batch_images = convert_from_path(
                    pdf_path,
                    dpi=self.dpi,
                    first_page=start_page,
                    last_page=end_page,
                    fmt='ppm',
                    thread_count=1,
                    **poppler_kwargs
                )

                # Traiter chaque page du lot
                for i, image in enumerate(batch_images):
                    page_num = start_page + i
                    try:
                        logger.info(f"📄 Page {page_num}/{total_pages} ({(page_num/total_pages*100):.1f}%)")

                        processed_image = self._preprocess_image_premium(image)
                        page_result = self._ocr_image_premium(processed_image)

                        text_parts.append(page_result["text"])
                        total_confidence += page_result["confidence"]
                        processed_pages += 1

                        if page_result.get("tables"):
                            tables_detected.extend(page_result["tables"])

                        # Libérer la mémoire
                        del image
                        del processed_image

                    except Exception as e:
                        logger.error(f"❌ Erreur page {page_num}: {e}")
                        text_parts.append(f"[ERREUR PAGE {page_num}: {str(e)}]")
                        processed_pages += 1

                # Libérer la mémoire du lot
                del batch_images

            except Exception as e:
                logger.error(f"❌ Erreur lot {start_page}-{end_page}: {e}")
                text_parts.append(f"[ERREUR LOT {start_page}-{end_page}: {str(e)}]")

        avg_confidence = total_confidence / processed_pages if processed_pages else 0

        logger.info(f"✅ Traitement par lots terminé: {processed_pages} pages traitées")

        return "\n\n".join(text_parts), {
            "pages_count": processed_pages,
            "confidence": avg_confidence,
            "tables_detected": tables_detected,
            "images_extracted": processed_pages
        }

    def _process_pdf_page_by_page(self, pdf_path: str, poppler_kwargs: dict) -> Tuple[str, Dict]:
        """Traiter un PDF page par page (fallback pour les PDF problématiques)"""
        text_parts = []
        total_confidence = 0
        tables_detected = []
        page_num = 1

        logger.info(f"🔄 Traitement page par page (mode fallback)")

        while True:
            try:
                # Essayer de convertir une page
                page_images = convert_from_path(
                    pdf_path,
                    dpi=self.dpi,
                    first_page=page_num,
                    last_page=page_num,
                    fmt='ppm',
                    **poppler_kwargs
                )

                if not page_images:
                    break  # Plus de pages

                logger.info(f"📄 Page {page_num}")

                image = page_images[0]
                processed_image = self._preprocess_image_premium(image)
                page_result = self._ocr_image_premium(processed_image)

                text_parts.append(page_result["text"])
                total_confidence += page_result["confidence"]

                if page_result.get("tables"):
                    tables_detected.extend(page_result["tables"])

                # Libérer la mémoire
                del image
                del processed_image
                del page_images

                page_num += 1

            except Exception as e:
                if "Unable to get page" in str(e) or "Invalid page number" in str(e):
                    # Fin du document
                    break
                else:
                    logger.error(f"❌ Erreur page {page_num}: {e}")
                    text_parts.append(f"[ERREUR PAGE {page_num}: {str(e)}]")
                    page_num += 1

                    # Éviter les boucles infinies
                    if page_num > 1000:
                        logger.error("❌ Arrêt: trop de pages (>1000)")
                        break

        processed_pages = page_num - 1
        avg_confidence = total_confidence / processed_pages if processed_pages else 0

        logger.info(f"✅ Traitement page par page terminé: {processed_pages} pages traitées")

        return "\n\n".join(text_parts), {
            "pages_count": processed_pages,
            "confidence": avg_confidence,
            "tables_detected": tables_detected,
            "images_extracted": processed_pages
        }

    def _preprocess_image_premium(self, image: Image.Image) -> Image.Image:
        """🚀 PRÉTRAITEMENT OPTIMISÉ POUR DOCUMENTS FINANCIERS"""
        try:
            processed_image = image

            # 🔧 1. REDIMENSIONNEMENT INTELLIGENT (optimal pour OCR)
            width, height = processed_image.size
            if width < 1200 or height < 1200:  # 🔧 Seuil augmenté pour meilleure qualité
                scale = max(1200 / width, 1200 / height)
                new_width = int(width * scale)
                new_height = int(height * scale)
                processed_image = processed_image.resize((new_width, new_height), Image.Resampling.LANCZOS)
                logger.info(f"🔄 Image redimensionnée: {width}x{height} → {new_width}x{new_height}")

            # 🔧 2. CONVERSION EN NIVEAUX DE GRIS SI NÉCESSAIRE
            if processed_image.mode != 'L':
                processed_image = processed_image.convert('L')

            # 🔧 3. AMÉLIORATION DU CONTRASTE (conservateur)
            if self.enhance_contrast:
                import numpy as np
                img_array = np.array(processed_image)

                # Amélioration douce du contraste
                img_array = np.clip(img_array * 1.1 + 10, 0, 255).astype(np.uint8)
                processed_image = Image.fromarray(img_array)
                logger.info("🔄 Contraste amélioré")

            return processed_image

        except Exception as e:
            logger.error(f"❌ Erreur prétraitement: {e}")
            return image  # Retourner l'image originale en cas d'erreur

    def _deskew_image(self, image: np.ndarray) -> np.ndarray:
        """Correction automatique de l'inclinaison"""
        try:
            gray = cv2.cvtColor(image, cv2.COLOR_BGR2GRAY)
            coords = np.column_stack(np.where(gray > 0))
            angle = cv2.minAreaRect(coords)[-1]

            if angle < -45:
                angle = -(90 + angle)
            else:
                angle = -angle

            if abs(angle) > 0.5:  # Seulement si inclinaison significative
                (h, w) = image.shape[:2]
                center = (w // 2, h // 2)
                M = cv2.getRotationMatrix2D(center, angle, 1.0)
                rotated = cv2.warpAffine(image, M, (w, h), flags=cv2.INTER_CUBIC, borderMode=cv2.BORDER_REPLICATE)
                logger.info(f"🔄 Correction inclinaison: {angle:.1f}°")
                return rotated
        except:
            pass

        return image

    def _correct_skew(self, img_array: np.ndarray) -> np.ndarray:
        """🚀 CORRECTION D'INCLINAISON AVANCÉE"""
        try:
            # Détecter les contours pour calculer l'angle
            edges = cv2.Canny(img_array, 50, 150, apertureSize=3)
            lines = cv2.HoughLines(edges, 1, np.pi/180, threshold=100)

            if lines is not None:
                angles = []
                for rho, theta in lines[:10]:  # Prendre les 10 premières lignes
                    angle = np.degrees(theta) - 90
                    if abs(angle) < 45:  # Ignorer les angles trop importants
                        angles.append(angle)

                if angles:
                    # Prendre l'angle médian pour éviter les outliers
                    median_angle = np.median(angles)

                    if abs(median_angle) > 0.5:  # Seulement si inclinaison significative
                        h, w = img_array.shape
                        center = (w // 2, h // 2)
                        M = cv2.getRotationMatrix2D(center, median_angle, 1.0)
                        rotated = cv2.warpAffine(img_array, M, (w, h),
                                               flags=cv2.INTER_CUBIC,
                                               borderMode=cv2.BORDER_REPLICATE)
                        logger.info(f"🔄 Correction inclinaison: {median_angle:.1f}°")
                        return rotated
        except Exception as e:
            logger.warning(f"⚠️ Correction inclinaison échouée: {e}")

        return img_array

    def _ocr_image_premium(self, image: Image.Image) -> Dict[str, any]:
        """OCR avec configuration EXACTE du Rust (lignes 271-334)"""

        # Sauvegarder l'image temporairement
        temp_image_path = os.path.join(self.temp_dir, f"temp_ocr_{int(time.time())}.png")
        image.save(temp_image_path)

        try:
            # Vérifier Tesseract EXACTEMENT comme Rust (lignes 251-269)
            logger.info("Vérification de Tesseract...")
            try:
                result = subprocess.run([self.tesseract_path, "--version"],
                                      capture_output=True, text=True, check=False)
                if result.returncode == 0:
                    logger.info(f"Tesseract est disponible: {result.stdout.strip()}")
                else:
                    logger.warning(f"Tesseract a retourné une erreur: {result.stderr.strip()}")
            except Exception as e:
                logger.error(f"Tesseract n'est pas disponible: {e}")
                raise RuntimeError(f"Tesseract is not available: {e}")

            # Commande Tesseract EXACTE du Rust (lignes 274-295)
            logger.info(f"Exécution de Tesseract OCR sur l'image: {temp_image_path}")

            # 🚀 CONFIGURATION TESSERACT OPTIMISÉE POUR STRUCTURE ET TABLEAUX
            cmd = [
                self.tesseract_path,
                temp_image_path,
                "stdout",
                "-l", "fra+eng",  # 🔧 Bi-langue pour meilleure reconnaissance
                "--psm", "6",     # 🔧 PSM 6 = bloc de texte uniforme (meilleur pour structure)
                "--oem", "3",     # 🔧 OEM 3 = LSTM + Legacy (plus robuste)
                "-c", "preserve_interword_spaces=1",
                "-c", "textord_tabfind_find_tables=1",      # 🔧 Détection de tableaux
                "-c", "textord_tablefind_recognize_tables=1", # 🔧 Reconnaissance de tableaux
                "-c", "textord_tabfind_vertical_text=1",    # 🔧 Texte vertical dans tableaux
                "-c", "textord_heavy_nr=1",                 # 🔧 Améliorer la détection de lignes
                "-c", "textord_show_initial_words=1",       # 🔧 Préserver l'ordre des mots
                "-c", "tessedit_char_blacklist=",           # 🔧 Ne pas exclure de caractères
                "-c", "tessedit_write_images=0",            # 🔧 Pas d'images de debug
            ]

            logger.info(f"Commande Tesseract: {' '.join(cmd)}")

            # Exécuter avec encodage UTF-8 correct
            result = subprocess.run(cmd, capture_output=True, text=True,
                                  encoding='utf-8', errors='replace', check=False)

            if result.returncode != 0:
                error_msg = f"Tesseract OCR failed: {result.stderr}"
                logger.error(error_msg)
                raise RuntimeError(error_msg)

            # Retourner le texte avec nettoyage d'encodage
            text = result.stdout

            # Nettoyer les problèmes d'encodage courants
            text = self._fix_encoding_issues(text)

            # Nettoyer les artefacts OCR (doublons, erreurs)
            text = self._clean_ocr_artifacts(text)

            logger.info(f"Texte extrait avec succès, longueur: {len(text)} caractères")

            if not text.strip():
                logger.warning("ATTENTION: Le texte extrait est vide!")
            elif len(text) < 100:
                logger.warning(f"ATTENTION: Texte extrait très court: {text}")

            return {
                "text": text.strip(),
                "confidence": 85,  # Valeur par défaut
                "tables": [],
                "words_count": len(text.split()),
                "detected_language": self.language
            }

        finally:
            # Nettoyer le fichier temporaire
            try:
                os.remove(temp_image_path)
            except:
                pass

    def _fix_encoding_issues(self, text: str) -> str:
        """Corriger les problèmes d'encodage courants dans l'OCR"""
        # Corrections COMPLÈTES pour tous les problèmes observés
        encoding_fixes = {
            # Problèmes spécifiques observés dans vos exemples
            'áµ‰': 'ème',
            '2áµ‰': '2ème',

            # Corrections complètes pour tous les accents français
            'BaccalaurÃ©at': 'Baccalauréat',
            'gÃ©nÃ©ral': 'général',
            'mathÃ©matiques': 'mathématiques',
            'Ã©conomiques': 'économiques',
            'DÃ©vellopement': 'Développement',
            'crÃ©ation': 'création',
            'dâ€™applications': "d'applications",
            'tÃ¢ches': 'tâches',
            'ComptabilitÃ©': 'Comptabilité',
            'trÃ©sorerie': 'trésorerie',
            'fiscalitÃ©': 'fiscalité',
            'financiÃ¨re': 'financière',
            'Ã©conomique': 'économique',
            'intÃ©grant': 'intégrant',
            'Ã‰valuation': 'Évaluation',
            'opportunitÃ©s': 'opportunités',
            'dâ€™investissement': "d'investissement",
            'prÃ©visions': 'prévisions',
            'financiÃ¨res': 'financières',
            'patrimoniale': 'patrimoniale',
            'modÃ©lisation': 'modélisation',

            # Patterns génériques pour tous les accents
            'annÃ©e': 'année',
            'Ã ': 'à',
            'Ã¡': 'á',
            'Ã¢': 'â',
            'Ã£': 'ã',
            'Ã¤': 'ä',
            'Ã¥': 'å',
            'Ã§': 'ç',
            'Ã¨': 'è',
            'Ã©': 'é',
            'Ãª': 'ê',
            'Ã«': 'ë',
            'Ã¬': 'ì',
            'Ã­': 'í',
            'Ã®': 'î',
            'Ã¯': 'ï',
            'Ã±': 'ñ',
            'Ã²': 'ò',
            'Ã³': 'ó',
            'Ã´': 'ô',
            'Ãµ': 'õ',
            'Ã¶': 'ö',
            'Ã¹': 'ù',
            'Ãº': 'ú',
            'Ã»': 'û',
            'Ã¼': 'ü',
            'Ã½': 'ý',
            'Ã¿': 'ÿ',

            # Majuscules accentuées (patterns sûrs seulement)
            'Ã€': 'À',
            'Ã‚': 'Â',
            'Ã„': 'Ä',
            'Ã…': 'Å',
            'Ã†': 'Æ',
            'Ã‡': 'Ç',
            'Ãˆ': 'È',
            'Ã‰': 'É',
            'ÃŠ': 'Ê',
            'Ã‹': 'Ë',
            'ÃŒ': 'Ì',
            'ÃŽ': 'Î',
            'Ã•': 'Õ',
            'Ã–': 'Ö',
            'Ã˜': 'Ø',
            'Ã™': 'Ù',
            'Ãš': 'Ú',
            'Ã›': 'Û',
            'Ãœ': 'Ü',

            # Apostrophes et guillemets
            'lâ€™': "l'",
            'dâ€™': "d'",
            'câ€™': "c'",
            'sâ€™': "s'",
            'nâ€™': "n'",
            'jâ€™': "j'",
            'mâ€™': "m'",
            'tâ€™': "t'",
            'â€™': "'",
            'â€œ': '"',
            'â€': '"',
            'â€"': '–',
            'â€"': '—',
            'â€¦': '…',

            # Espaces et caractères spéciaux
            'Â ': ' ',
            'Â°': '°',
            'Â«': '«',
            'Â»': '»',

            # Mots complets problématiques
            'grÃ¢ce': 'grâce',
            'Ã©quipe': 'équipe',
            'Ã©tudiant': 'étudiant',
            'compÃ©tences': 'compétences',
            'FormÃ©': 'Formé',
            'persÃ©vÃ©rant': 'persévérant',
            'motivÃ©': 'motivé',
        }

        # Appliquer les corrections
        for wrong, correct in encoding_fixes.items():
            text = text.replace(wrong, correct)

        return text

    def _clean_ocr_artifacts(self, text: str) -> str:
        """Nettoyer les artefacts OCR courants (doublons, erreurs)"""

        # 1. Nettoyer les doublons de mots répétés
        import re

        # Patterns de doublons courants
        duplicates_patterns = [
            # Doublons de sections
            (r'(PROFIL PERSONNEL){2,}', r'PROFIL PERSONNEL'),
            (r'(ENSEIGNEMENT){2,}', r'ENSEIGNEMENT'),
            (r'(COMPÉTENCES){2,}', r'COMPÉTENCES'),
            (r'(BÉNÉVOLAT ET CENTRES){2,}', r'BÉNÉVOLAT ET CENTRES'),
            (r'(COORDONNÉES){2,}', r'COORDONNÉES'),
            (r'(EXPERIENCE){2,}', r'EXPERIENCE'),

            # Doublons de mots génériques
            (r'\b(\w+)\s+\1\b', r'\1'),  # Mot répété deux fois
            (r'\b(\w+)\s+\1\s+\1\b', r'\1'),  # Mot répété trois fois
        ]

        for pattern, replacement in duplicates_patterns:
            text = re.sub(pattern, replacement, text, flags=re.IGNORECASE)

        # 2. Corrections de caractères OCR courants
        ocr_fixes = {
            # Corrections spécifiques observées
            'DeMOIIens': 'Demolliens',
            'Dévellopement': 'Développement',
            'Powererpoint': 'PowerPoint',
            'Patticipation': 'Participation',
            'd ravail': 'du travail',
            'Trang,': 'Trading,',

            # Corrections génériques
            'II': 'll',  # Double I souvent confondu avec ll
            'rn': 'm',   # rn souvent confondu avec m
            '|': 'l',    # | souvent confondu avec l
            '0': 'O',    # 0 souvent confondu avec O dans les noms
        }

        for error, correction in ocr_fixes.items():
            text = text.replace(error, correction)

        # 3. Nettoyer les espaces multiples
        text = re.sub(r'\s+', ' ', text)
        text = re.sub(r'\n\s*\n\s*\n', '\n\n', text)  # Max 2 sauts de ligne consécutifs

        # 4. Nettoyer les caractères isolés problématiques
        text = re.sub(r'\s+[|]\s+', ' ', text)  # Pipes isolés
        text = re.sub(r'\s+[.]\s+', '. ', text)  # Points isolés

        return text.strip()

    def _detect_table_structure(self, ocr_data: Dict) -> List[Dict]:
        """Détection et structuration des tableaux"""
        tables = []

        # Analyser la disposition des mots pour détecter des structures tabulaires
        words_by_line = {}

        for i, text in enumerate(ocr_data['text']):
            if text.strip() and int(ocr_data['conf'][i]) > self.confidence_threshold:
                top = ocr_data['top'][i]
                line_key = top // 20  # Grouper par lignes approximatives

                if line_key not in words_by_line:
                    words_by_line[line_key] = []

                words_by_line[line_key].append({
                    'text': text,
                    'left': ocr_data['left'][i],
                    'top': top,
                    'width': ocr_data['width'][i],
                    'height': ocr_data['height'][i]
                })

        # Détecter les structures de tableau (lignes avec colonnes alignées)
        for line_key, words in words_by_line.items():
            if len(words) >= 3:  # Au moins 3 colonnes
                words_sorted = sorted(words, key=lambda x: x['left'])

                # Vérifier l'alignement vertical
                if self._is_table_row(words_sorted):
                    tables.append({
                        'line': line_key,
                        'columns': [w['text'] for w in words_sorted],
                        'confidence': 'medium'
                    })

        if tables:
            logger.info(f"📊 {len(tables)} lignes de tableau détectées")

        return tables

    def _is_table_row(self, words: List[Dict]) -> bool:
        """Vérifier si une ligne de mots forme une structure de tableau"""
        if len(words) < 3:
            return False

        # Vérifier l'espacement régulier entre les colonnes
        spacings = []
        for i in range(len(words) - 1):
            spacing = words[i+1]['left'] - (words[i]['left'] + words[i]['width'])
            spacings.append(spacing)

        # Les espacements doivent être relativement constants
        if spacings:
            avg_spacing = sum(spacings) / len(spacings)
            return all(abs(s - avg_spacing) < 50 for s in spacings)

        return False

    def _format_table_text(self, table_data: List[List[str]]) -> str:
        """Formater les données de tableau en Markdown parfait"""
        if not table_data:
            return ""

        # Nettoyer et normaliser les données
        cleaned_data = []
        for row in table_data:
            cleaned_row = []
            for cell in row:
                # Nettoyer le contenu des cellules
                cell_text = str(cell).strip().replace('\n', ' ').replace('|', '\\|')
                if not cell_text:
                    cell_text = " "
                cleaned_row.append(cell_text)
            cleaned_data.append(cleaned_row)

        if not cleaned_data:
            return ""

        # Calculer la largeur maximale de chaque colonne
        max_cols = max(len(row) for row in cleaned_data)
        col_widths = []

        for col_idx in range(max_cols):
            max_width = 0
            for row in cleaned_data:
                if col_idx < len(row):
                    max_width = max(max_width, len(row[col_idx]))
            col_widths.append(max(max_width, 3))  # Minimum 3 caractères

        # Générer le tableau Markdown
        markdown_lines = []

        for row_idx, row in enumerate(cleaned_data):
            # Compléter la ligne si nécessaire
            while len(row) < max_cols:
                row.append(" ")

            # Formater la ligne Markdown
            formatted_cells = []
            for col_idx, cell in enumerate(row):
                if col_idx < len(col_widths):
                    formatted_cells.append(f" {cell.ljust(col_widths[col_idx])} ")
                else:
                    formatted_cells.append(f" {cell} ")

            markdown_line = "|" + "|".join(formatted_cells) + "|"
            markdown_lines.append(markdown_line)

            # Ajouter la ligne de séparation après l'en-tête (première ligne)
            if row_idx == 0:
                separator_cells = []
                for col_idx in range(max_cols):
                    if col_idx < len(col_widths):
                        separator_cells.append("-" * (col_widths[col_idx] + 2))
                    else:
                        separator_cells.append("---")
                separator_line = "|" + "|".join(separator_cells) + "|"
                markdown_lines.append(separator_line)

        return '\n'.join(markdown_lines)

    def _integrate_tables_in_text(self, page, tables) -> str:
        """Intégrer les tableaux dans le texte à leur position correcte"""
        try:
            # Obtenir le texte avec les positions
            text_dict = page.get_text("dict")

            # Créer une liste des éléments avec leurs positions
            elements = []

            # Ajouter les blocs de texte
            for block in text_dict["blocks"]:
                if "lines" in block:
                    for line in block["lines"]:
                        for span in line["spans"]:
                            if span["text"].strip():
                                elements.append({
                                    "type": "text",
                                    "content": span["text"],
                                    "bbox": span["bbox"],
                                    "y": span["bbox"][1]  # Position Y pour le tri
                                })

            # Ajouter les tableaux
            for i, table in enumerate(tables):
                table_bbox = table.bbox
                table_data = table.extract()
                if table_data:
                    table_markdown = self._format_table_text(table_data)
                    elements.append({
                        "type": "table",
                        "content": f"\n\n{table_markdown}\n\n",
                        "bbox": table_bbox,
                        "y": table_bbox[1]
                    })

            # Trier par position Y
            elements.sort(key=lambda x: x["y"])

            # Reconstruire le texte
            result_text = ""
            for element in elements:
                if element["type"] == "text":
                    result_text += element["content"] + " "
                else:  # table
                    result_text += element["content"]

            return result_text.strip()

        except Exception as e:
            logger.warning(f"Erreur intégration tableaux: {e}")
            return None

    def _improve_text_structure(self, text: str) -> str:
        """Améliorer la structure générale du texte"""
        if not text:
            return text

        # Normaliser les espaces et les sauts de ligne
        text = re.sub(r'\n\s*\n\s*\n+', '\n\n', text)  # Réduire les sauts de ligne multiples
        text = re.sub(r'[ \t]+', ' ', text)  # Normaliser les espaces
        text = re.sub(r' +\n', '\n', text)  # Supprimer les espaces en fin de ligne

        # Améliorer la ponctuation
        text = re.sub(r'([.!?])\s*([A-Z])', r'\1\n\n\2', text)  # Saut de ligne après les phrases
        text = re.sub(r'([a-z])\s*([A-Z][a-z]+:)', r'\1\n\n\2', text)  # Titres avec deux points

        # Détecter et formater les listes
        lines = text.split('\n')
        formatted_lines = []
        in_list = False

        for line in lines:
            line = line.strip()
            if not line:
                formatted_lines.append('')
                in_list = False
                continue

            # Détecter les éléments de liste
            if re.match(r'^[-•*]\s+', line) or re.match(r'^\d+[.)]\s+', line):
                if not in_list:
                    formatted_lines.append('')  # Ligne vide avant la liste
                formatted_lines.append(line)
                in_list = True
            else:
                if in_list:
                    formatted_lines.append('')  # Ligne vide après la liste
                formatted_lines.append(line)
                in_list = False

        return '\n'.join(formatted_lines)

    def _process_image_premium(self, image_path: str) -> Dict[str, any]:
        """Traitement premium d'une image"""
        try:
            image = Image.open(image_path)

            # Prétraitement premium
            processed_image = self._preprocess_image_premium(image)

            # OCR premium
            result = self._ocr_image_premium(processed_image)
            result["method_used"] = "image_premium"
            result["pages_count"] = 1

            return result

        except Exception as e:
            logger.error(f"❌ Traitement image premium échoué: {e}")
            raise RuntimeError(f"Erreur traitement image: {e}")

    def _process_text_file(self, file_path: str) -> Dict[str, any]:
        """Traitement d'un fichier texte avec détection d'encodage avancée"""
        encodings = ['utf-8', 'utf-8-sig', 'latin-1', 'cp1252', 'iso-8859-1']

        for encoding in encodings:
            try:
                with open(file_path, 'r', encoding=encoding) as f:
                    text = f.read()

                return {
                    "text": text,
                    "method_used": f"text_direct_{encoding}",
                    "confidence": 100,
                    "pages_count": 1,
                    "detected_language": self._detect_language(text)
                }

            except UnicodeDecodeError:
                continue

        raise ValueError("Impossible de décoder le fichier texte avec les encodages supportés")

    def _detect_language(self, text: str) -> str:
        """Détection simple de la langue du texte"""
        if not text or len(text) < 50:
            return "unknown"

        # Mots français courants
        french_words = ['le', 'de', 'et', 'à', 'un', 'il', 'être', 'avoir', 'ne', 'je', 'son', 'que', 'se', 'qui', 'ce', 'dans', 'en', 'du', 'elle', 'au', 'de', 'ce', 'le', 'tout', 'mais', 'pour', 'sur', 'avec', 'ne', 'se', 'pas', 'tout']

        # Mots anglais courants
        english_words = ['the', 'of', 'and', 'to', 'in', 'is', 'you', 'that', 'it', 'he', 'was', 'for', 'on', 'are', 'as', 'with', 'his', 'they', 'i', 'at', 'be', 'this', 'have', 'from', 'or', 'one', 'had', 'by', 'word', 'but']

        text_lower = text.lower()
        french_count = sum(1 for word in french_words if f' {word} ' in text_lower)
        english_count = sum(1 for word in english_words if f' {word} ' in text_lower)

        if french_count > english_count:
            return "french"
        elif english_count > french_count:
            return "english"
        else:
            return "mixed"

    def _calculate_quality_score(self, result: Dict) -> float:
        """Calculer un score de qualité du traitement"""
        score = 0

        # Confiance OCR (40% du score)
        if "confidence" in result:
            score += (result["confidence"] / 100) * 40

        # Longueur du texte extrait (30% du score)
        text_length = len(result.get("text", ""))
        if text_length > 0:
            # Score basé sur la longueur (plafonné à 30)
            length_score = min(30, (text_length / 1000) * 30)
            score += length_score

        # Détection de structures (20% du score)
        if result.get("tables_detected"):
            score += 15  # Bonus pour tableaux détectés
        if result.get("detected_language") != "unknown":
            score += 5   # Bonus pour langue détectée

        # Temps de traitement (10% du score)
        processing_time = result.get("processing_time", float('inf'))
        if processing_time < 30:  # Moins de 30 secondes = bon
            score += 10
        elif processing_time < 60:  # Moins de 1 minute = acceptable
            score += 5

        return min(100, score)  # Plafonner à 100

    def _process_word_document(self, file_path: str) -> Dict[str, any]:
        """Traitement rapide des documents Word (.docx)"""
        if not DOCX_AVAILABLE:
            raise RuntimeError("python-docx non installé. Installez avec: pip install python-docx")

        try:
            logger.info("📄 Extraction Word (.docx) - RAPIDE")
            doc = Document(file_path)

            text_parts = []
            tables_detected = []

            # Extraire le texte des paragraphes
            for paragraph in doc.paragraphs:
                if paragraph.text.strip():
                    text_parts.append(paragraph.text.strip())

            # Extraire les tableaux en Markdown
            for i, table in enumerate(doc.tables):
                table_data = []
                for row in table.rows:
                    row_data = []
                    for cell in row.cells:
                        cell_text = cell.text.strip().replace('\n', ' ')
                        row_data.append(cell_text)
                    table_data.append(row_data)

                if table_data:
                    table_markdown = self._format_table_text(table_data)
                    text_parts.append(f"\n\n## Tableau {i+1}\n\n{table_markdown}\n\n")
                    tables_detected.append(f"Tableau {i+1}")

            # Améliorer la structure
            full_text = "\n\n".join(text_parts)
            full_text = self._improve_text_structure(full_text)

            return {
                "text": full_text,
                "method_used": "word_direct",
                "confidence": 100,  # Extraction directe = confiance maximale
                "detected_language": "fra",
                "pages_count": 1,
                "tables_detected": tables_detected,
                "images_extracted": 0
            }

        except Exception as e:
            logger.error(f"❌ Erreur traitement Word: {e}")
            raise RuntimeError(f"Erreur traitement Word: {e}")

    def _process_powerpoint_document(self, file_path: str) -> Dict[str, any]:
        """Traitement rapide des présentations PowerPoint (.pptx)"""
        if not PPTX_AVAILABLE:
            raise RuntimeError("python-pptx non installé. Installez avec: pip install python-pptx")

        try:
            logger.info("📊 Extraction PowerPoint (.pptx) - RAPIDE")
            prs = Presentation(file_path)

            text_parts = []
            tables_detected = []
            slide_count = 0

            for slide_num, slide in enumerate(prs.slides, 1):
                slide_text = []
                slide_text.append(f"## Diapositive {slide_num}")

                # Extraire le texte des formes
                for shape in slide.shapes:
                    if hasattr(shape, "text") and shape.text.strip():
                        slide_text.append(shape.text.strip())

                    # Extraire les tableaux
                    if shape.has_table:
                        table_data = []
                        for row in shape.table.rows:
                            row_data = []
                            for cell in row.cells:
                                cell_text = cell.text.strip().replace('\n', ' ')
                                row_data.append(cell_text)
                            table_data.append(row_data)

                        if table_data:
                            table_markdown = self._format_table_text(table_data)
                            slide_text.append(f"\n### Tableau Diapositive {slide_num}\n\n{table_markdown}\n")
                            tables_detected.append(f"Tableau Diapositive {slide_num}")

                if len(slide_text) > 1:  # Plus que juste le titre
                    text_parts.append("\n".join(slide_text))
                    slide_count += 1

            # Améliorer la structure
            full_text = "\n\n".join(text_parts)
            full_text = self._improve_text_structure(full_text)

            return {
                "text": full_text,
                "method_used": "powerpoint_direct",
                "confidence": 100,  # Extraction directe = confiance maximale
                "detected_language": "fra",
                "pages_count": slide_count,
                "tables_detected": tables_detected,
                "images_extracted": 0
            }

        except Exception as e:
            logger.error(f"❌ Erreur traitement PowerPoint: {e}")
            raise RuntimeError(f"Erreur traitement PowerPoint: {e}")

# Fonction utilitaire pour compatibilité ClaraVerse
def process_document_premium(file_path: str) -> Dict[str, any]:
    """
    Fonction utilitaire pour traiter un document avec OCR premium
    Compatible avec l'interface ClaraVerse
    """
    processor = PremiumOcrProcessor()
    return processor.process_document(file_path)
