/**
 * 🚀 PERFECT COMPRESSOR RÉVOLUTIONNAIRE - WeMa IA (Frontend)
 * Interface TypeScript pour la compression intelligente des conversations
 * Communique avec le backend Python pour la compression par LLM
 */

import { Message } from '../../../types/chat';
import { Document } from '../../../types/document';

export interface CompressionStats {
  compressed: boolean;
  original_tokens?: number;
  compressed_tokens?: number;
  compression_ratio?: number;
  processing_time?: number;
  method?: string;
  error?: string;
  reason?: string;
}

export interface CompressionConfig {
  compression_threshold: number;
  max_tokens: number;
  target_tokens: number;
  compression_model: string;
}

export interface CompressionMetrics {
  total_compressions: number;
  total_tokens_saved: number;
  average_compression_ratio: number;
  last_compression_time: string | null;
  configuration: CompressionConfig;
}

export class PerfectCompressor {
  private baseUrl: string;
  private compressionThreshold: number = 20000;
  private maxTokens: number = 35000;
  private targetTokens: number = 15000;

  constructor(baseUrl: string = 'http://localhost:5001') {
    this.baseUrl = baseUrl;
    console.log('🚀 Perfect Compressor Frontend initialisé - WeMa IA');
    console.log(`   📊 Seuil: ${this.compressionThreshold} tokens`);
    console.log(`   🎯 Cible: ${this.targetTokens} tokens`);
    console.log(`   ⚠️ Maximum: ${this.maxTokens} tokens`);
  }

  /**
   * Déterminer si la compression est nécessaire
   */
  shouldCompress(messages: Message[], documents?: Document[]): boolean {
    const totalTokens = this.estimateTokens(messages, documents);
    
    console.log(`📊 Estimation tokens: ${totalTokens}`);
    
    if (totalTokens > this.maxTokens) {
      console.warn(`⚠️ DÉPASSEMENT MAXIMUM: ${totalTokens} > ${this.maxTokens}`);
      return true;
    } else if (totalTokens > this.compressionThreshold) {
      console.log(`🔄 Compression recommandée: ${totalTokens} > ${this.compressionThreshold}`);
      return true;
    } else {
      console.log(`✅ Pas de compression nécessaire: ${totalTokens} < ${this.compressionThreshold}`);
      return false;
    }
  }

  /**
   * Compresser la conversation via le backend
   */
  async compressConversation(messages: Message[], documents?: Document[]): Promise<{
    messages: Message[];
    stats: CompressionStats;
  }> {
    const startTime = Date.now();
    
    if (!messages || messages.length === 0) {
      return {
        messages,
        stats: { compressed: false, reason: "Conversation vide" }
      };
    }

    const originalTokens = this.estimateTokens(messages, documents);
    console.log(`🔄 Début compression: ${originalTokens} tokens`);

    try {
      const response = await fetch(`${this.baseUrl}/api/compress-conversation`, {
        method: 'POST',
        headers: {
          'Content-Type': 'application/json',
        },
        body: JSON.stringify({
          conversation: messages,
          documents: documents || []
        })
      });

      if (!response.ok) {
        throw new Error(`Erreur API: ${response.status}`);
      }

      const result = await response.json();
      
      const processingTime = (Date.now() - startTime) / 1000;
      
      console.log(`✅ Compression terminée en ${processingTime.toFixed(1)}s`);
      if (result.stats.compressed) {
        console.log(`   📊 ${result.stats.original_tokens} → ${result.stats.compressed_tokens} tokens`);
        console.log(`   📉 Ratio: ${(result.stats.compression_ratio * 100).toFixed(1)}%`);
      }

      return {
        messages: result.compressed_conversation || messages,
        stats: {
          ...result.stats,
          processing_time: processingTime
        }
      };

    } catch (error) {
      console.error('❌ Erreur compression:', error);
      
      // Fallback: compression simple côté client
      const compressedMessages = this.simpleCompressionFallback(messages);
      const compressedTokens = this.estimateTokens(compressedMessages, documents);
      
      return {
        messages: compressedMessages,
        stats: {
          compressed: true,
          original_tokens: originalTokens,
          compressed_tokens: compressedTokens,
          compression_ratio: (originalTokens - compressedTokens) / originalTokens,
          processing_time: (Date.now() - startTime) / 1000,
          method: "simple_fallback_frontend",
          error: error instanceof Error ? error.message : String(error)
        }
      };
    }
  }

  /**
   * Obtenir les statistiques de compression
   */
  async getStats(): Promise<CompressionMetrics | null> {
    try {
      const response = await fetch(`${this.baseUrl}/api/compression-stats`);
      if (!response.ok) {
        throw new Error(`Erreur API: ${response.status}`);
      }
      return await response.json();
    } catch (error) {
      console.error('❌ Erreur récupération stats:', error);
      return null;
    }
  }

  /**
   * Réinitialiser les statistiques
   */
  async resetStats(): Promise<boolean> {
    try {
      const response = await fetch(`${this.baseUrl}/api/compression-stats/reset`, {
        method: 'POST'
      });
      return response.ok;
    } catch (error) {
      console.error('❌ Erreur reset stats:', error);
      return false;
    }
  }

  /**
   * Estimation rapide du nombre de tokens
   */
  private estimateTokens(messages: Message[], documents?: Document[]): number {
    let totalChars = 0;

    // Compter les caractères des messages
    for (const message of messages) {
      if (typeof message.content === 'string') {
        totalChars += message.content.length;
      } else if (Array.isArray(message.content)) {
        for (const item of message.content) {
          if (typeof item === 'object' && 'text' in item && typeof item.text === 'string') {
            totalChars += item.text.length;
          }
        }
      }
    }

    // Compter les caractères des documents
    if (documents) {
      for (const doc of documents) {
        if (doc.content && typeof doc.content === 'string') {
          totalChars += doc.content.length;
        }
      }
    }

    // Estimation: ~4 caractères par token pour le français
    return Math.floor(totalChars / 4);
  }

  /**
   * Compression simple en cas d'échec du backend
   */
  private simpleCompressionFallback(messages: Message[]): Message[] {
    if (messages.length <= 3) {
      return messages;
    }

    const compressed: Message[] = [];

    // Premier message (souvent système)
    if (messages[0]?.role === 'system') {
      compressed.push(messages[0]);
    }

    // Résumé des messages du milieu
    const middleMessages = messages.slice(1, -2);
    if (middleMessages.length > 0) {
      compressed.push({
        role: 'system',
        content: `[Résumé de ${middleMessages.length} messages précédents - compression automatique frontend]`,
        timestamp: Date.now()
      });
    }

    // Derniers messages
    compressed.push(...messages.slice(-2));

    return compressed;
  }

  /**
   * Mettre à jour la configuration
   */
  updateConfiguration(config: Partial<CompressionConfig>): void {
    if (config.compression_threshold !== undefined) {
      this.compressionThreshold = config.compression_threshold;
      console.log(`📊 Seuil de compression mis à jour: ${this.compressionThreshold}`);
    }
    
    if (config.max_tokens !== undefined) {
      this.maxTokens = config.max_tokens;
      console.log(`⚠️ Maximum de tokens mis à jour: ${this.maxTokens}`);
    }
    
    if (config.target_tokens !== undefined) {
      this.targetTokens = config.target_tokens;
      console.log(`🎯 Cible de tokens mise à jour: ${this.targetTokens}`);
    }
  }
}

// Instance singleton
export const perfectCompressor = new PerfectCompressor();

// Export par défaut
export default perfectCompressor;
