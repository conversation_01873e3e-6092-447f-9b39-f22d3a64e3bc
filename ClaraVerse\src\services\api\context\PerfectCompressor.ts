/**
 * 🚀 COMPRESSEUR PARFAIT - WeMa IA
 * 
 * Système de compression intelligent PROPRE et MAINTENABLE
 * - SEULEMENT la compression intelligente
 * - Le LLM choisit quels messages garder
 * - Code minimal et efficace
 */

import type { ClaraMessage } from '../../../types/clara_assistant_types';

export interface SmartCompressionConfig {
  targetTokens?: number;
  maxMessages?: number;
  preserveRecent?: number;
}

export interface SmartCompressionResult {
  messagesToKeep: string[];
  messagesToSummarize: string[];
  summaryOfRemoved: string;
}

export class PerfectCompressor {
  private readonly SERVER_URL = 'http://localhost:5001/proxy/ollama';
  private readonly COMPRESSION_MODEL = 'qwen3-14b-optimized:latest';

  /**
   * 🧠 COMPRESSION INTELLIGENTE PRINCIPALE
   */
  public async compress(
    messages: ClaraMessage[],
    config: SmartCompressionConfig = {},
    ragContext?: string
  ): Promise<ClaraMessage[]> {
    // Configuration non utilisée dans la réécriture complète

    // 🚀 CONSTRUIRE LE SYSTEM PROMPT POUR LE CALCUL
    const systemPrompt = this.buildSystemPromptForCompression(messages, ragContext);

    // Vérifier si compression nécessaire (avec contexte RAG + system prompt)
    if (!this.shouldCompress(messages, ragContext, systemPrompt)) {
      console.log(`ℹ️ Compression non nécessaire: ${messages.length} messages`);
      return messages;
    }

    console.log(`🧠 Compression intelligente: ${messages.length} messages → réécriture complète du contexte`);

    try {
      if (messages.length === 0) {
        console.log('🔄 Pas de messages à analyser, retour direct');
        return messages;
      }

      // 🚀 ANALYSER TOUS LES MESSAGES (conversation + RAG) pour compression massive
      console.log('🔄 Appel LLM pour réécriture complète de TOUT le contexte...');
      const analysis = await this.analyzeWithLLM(messages);
      console.log('✅ Analyse LLM terminée:', analysis);

      // Construire le résultat - RÉÉCRITURE COMPLÈTE DE TOUT
      const result = this.buildResult([], messages, analysis);

      console.log(`✅ Compression terminée: ${messages.length} → ${result.length} messages`);
      return result;

    } catch (error) {
      console.error('❌ ERREUR COMPRESSION - PAS DE FALLBACK:', error);
      throw new Error(`Compression failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * � CONSTRUIRE LE SYSTEM PROMPT POUR LE CALCUL (IDENTIQUE CHATMANAGER)
   */
  private buildSystemPromptForCompression(messages: ClaraMessage[], ragContext?: string): string {
    let systemPrompt = "Tu es WeMa IA, un assistant intelligent et utile. IMPORTANT: Réponds TOUJOURS en français sauf si l'utilisateur demande explicitement une autre langue. Sois direct, précis et évite les réflexions inutiles.";

    // 1. Ajouter le contexte RAG externe si disponible
    if (ragContext) {
      systemPrompt += `\n\nUtilise les documents suivants pour répondre à la question de l'utilisateur:\n${ragContext}`;
    }

    // 2. Extraire et ajouter les documents RAG depuis l'historique
    const ragMessagesInHistory = messages.filter(msg =>
      msg.role === 'system' && msg.metadata?.type === 'rag'
    );

    if (ragMessagesInHistory.length > 0) {
      const documentsFromHistory = ragMessagesInHistory.map(msg => {
        const content = msg.content || '';
        const docName = msg.metadata?.documentName || 'Document';
        const cleanContent = content.replace(/^📄\s*Document RAG:\s*[^\n]*\n\n?/, '').trim();
        return `[Document: ${docName}]\n${cleanContent}`;
      }).join('\n\n---\n\n');

      systemPrompt += `\n\nUtilise les documents suivants pour répondre à la question de l'utilisateur:\n${documentsFromHistory}`;
    }

    return systemPrompt;
  }

  /**
   * �📊 VÉRIFIER SI COMPRESSION NÉCESSAIRE - CALCUL IDENTIQUE AU CONTEXT MANAGER
   */
  public shouldCompress(messages: ClaraMessage[], ragContext?: string, systemPrompt?: string): boolean {
    // 🎯 COMPTEUR DE TOKENS AMÉLIORÉ - Plus précis que chars/4
    const estimateTokens = (text: string): number => {
      if (!text) return 0;
      // Méthode plus précise : compter les mots + caractères spéciaux
      const words = text.split(/\s+/).filter(word => word.length > 0);
      const specialChars = (text.match(/[^\w\s]/g) || []).length;
      // Estimation : 1 mot ≈ 1.3 tokens, caractères spéciaux ≈ 0.5 tokens
      return Math.ceil(words.length * 1.3 + specialChars * 0.5);
    };

    // 1. Calculer les tokens des messages de conversation (SANS les messages RAG)
    const conversationMessages = messages.filter(msg =>
      !(msg.role === 'system' && msg.metadata?.type === 'rag')
    );
    const conversationTokens = estimateTokens(conversationMessages.map(msg => msg.content || '').join(' '));

    // 2. Extraire le contexte RAG depuis les messages RAG en base
    const ragMessagesInHistory = messages.filter(msg =>
      msg.role === 'system' && msg.metadata?.type === 'rag'
    );
    const ragTokensFromHistory = estimateTokens(ragMessagesInHistory.map(msg => msg.content || '').join(' '));

    // 3. Ajouter le contexte RAG passé en paramètre (si disponible)
    const ragTokensFromContext = ragContext ? estimateTokens(ragContext) : 0;

    // 4. Calculer les tokens du system prompt (IDENTIQUE Context Manager)
    const systemTokens = systemPrompt ? estimateTokens(systemPrompt) : 0;

    const totalRagTokens = ragTokensFromHistory + ragTokensFromContext;
    const totalTokens = conversationTokens + totalRagTokens + systemTokens;

    const hasCompressed = messages.some(msg =>
      msg.metadata?.isCompressed ||
      msg.content.includes('📋 **Résumé intelligent**')
    );

    console.log(`🔍 shouldCompress IDENTIQUE Context Manager: ${conversationTokens} tokens conversation + ${totalRagTokens} tokens RAG + ${systemTokens} tokens system = ${totalTokens} tokens total`);

    // 🧠 LOGIQUE INTELLIGENTE DE COMPRESSION
    const exceedsTokens = totalTokens > 20000; // 🔧 SEUIL RÉDUIT À 20K
    const notCompressed = !hasCompressed;

    // 🔍 DÉTECTER LES GROS DOCUMENTS (> 10K caractères)
    const hasLargeDocuments = messages.some(msg =>
      msg.role === 'system' &&
      msg.metadata?.type === 'rag' &&
      msg.content.length > 10000
    );

    // 🔍 DÉTECTER LES MESSAGES UTILISATEUR VOLUMINEUX (> 10K caractères)
    const hasLargeUserMessages = messages.some(msg =>
      msg.role === 'user' &&
      msg.content.length > 10000
    );

    // 🎯 CRITÈRES DE COMPRESSION INTELLIGENTS
    const criteriaTokens = exceedsTokens; // Toujours compresser si > 25K tokens
    const criteriaLargeDocs = hasLargeDocuments && messages.length > 2; // Gros docs après 2 messages
    const criteriaLargeMessages = hasLargeUserMessages && messages.length > 2; // Gros messages après 2 messages
    const criteriaMany = messages.length > 8; // Beaucoup de messages (seuil classique)

    console.log(`🔍 DIAGNOSTIC INTELLIGENT:`);
    console.log(`  📊 Tokens: ${totalTokens} > 20000? ${exceedsTokens}`);
    console.log(`  📄 Gros documents (>10K): ${hasLargeDocuments}`);
    console.log(`  💬 Gros messages (>10K): ${hasLargeUserMessages}`);
    console.log(`  📝 Messages: ${messages.length}`);
    console.log(`  🔒 Déjà compressé: ${hasCompressed}`);
    console.log(`  ⚠️ ATTENTION: Calcul différent du ContextManager - à investiguer !`);

    const shouldCompress = notCompressed && (criteriaTokens || criteriaLargeDocs || criteriaLargeMessages || criteriaMany);

    console.log(`� CRITÈRES: tokens=${criteriaTokens} | largeDocs=${criteriaLargeDocs} | largeMsg=${criteriaLargeMessages} | many=${criteriaMany}`);
    console.log(`🔍 RÉSULTAT shouldCompress: ${shouldCompress}`);

    return shouldCompress;
  }

  /**
   * 🔍 ANALYSER AVEC LE LLM
   */
  private async analyzeWithLLM(
    messages: ClaraMessage[]
  ): Promise<SmartCompressionResult> {
    const messagesList = messages.map((msg, index) => 
      `[${msg.id}] ${msg.role} (${index + 1}): ${msg.content.substring(0, 150)}${msg.content.length > 150 ? '...' : ''}`
    ).join('\n');

    const prompt = `🧠 MISSION CRITIQUE: Tu es un GESTIONNAIRE INTELLIGENT de contexte conversationnel. Ta mission est d'optimiser le contexte en préservant TOUTE l'information importante.

📋 CONVERSATION À OPTIMISER (${messages.length} messages):
${messagesList}

🎯 OBJECTIF: Réorganiser et optimiser le contexte pour rester sous 35K tokens tout en préservant l'intégralité des informations importantes.

🧠 GESTION INTELLIGENTE REQUISE:
1. **PRÉSERVER** - Toutes les informations critiques, décisions, données importantes
2. **RÉORGANISER** - Structurer logiquement par thèmes/sujets
3. **RÉSUMER** - Les échanges redondants en gardant les points clés
4. **CLARIFIER** - Ajouter des transitions et du contexte si nécessaire
5. **TRACER** - Indiquer clairement ce qui a été modifié/supprimé

🔍 STRATÉGIE D'OPTIMISATION INTELLIGENTE:
- Garder les documents RAG avec résumés détaillés (pas 2 phrases !)
- Préserver les décisions et solutions importantes
- Fusionner les échanges similaires en gardant la substance
- Maintenir la chronologie et la cohérence
- Viser 15-20K tokens (pas 100 tokens ridicules)

⚠️ RÈGLES DU GESTIONNAIRE:
- Réponse UNIQUEMENT en JSON valide
- Préservation MAXIMALE des informations importantes
- Résumés détaillés et informatifs (pas télégraphiques)
- Indication claire des modifications apportées

FORMAT JSON OBLIGATOIRE:
{
  "rewrittenContext": "Contexte intelligemment optimisé avec préservation maximale des informations. Résumés détaillés des documents, préservation des décisions importantes, structure claire et logique. Taille cible: 15-20K tokens.",
  "modificationsApportees": "Description claire de ce qui a été supprimé, fusionné ou réorganisé pour la transparence."
}`;

    const response = await this.callOllama([
      {
        role: 'system',
        content: `Tu es un EXPERT en gestion de bases de données conversationnelles et optimisation de contexte IA.

🎯 MISSION: Analyser et optimiser les conversations pour maintenir une base de données parfaite.

🧠 COMPÉTENCES:
- Analyse sémantique avancée des conversations
- Identification des messages critiques vs redondants
- Préservation de la cohérence narrative
- Optimisation du ratio information/tokens

⚠️ RÈGLES ABSOLUES:
- Réponse UNIQUEMENT en JSON valide (pas de texte avant/après)
- Précision maximale dans la sélection des IDs
- Résumés informatifs et concis
- Zéro perte d'information critique`
      },
      {
        role: 'user',
        content: prompt
      }
    ]);

    return this.parseResponse(response);
  }

  /**
   * 🔧 APPEL OLLAMA
   */
  private async callOllama(messages: any[]): Promise<string> {
    console.log(`🔄 Appel Ollama: ${this.SERVER_URL}/api/chat avec modèle ${this.COMPRESSION_MODEL}`);

    const response = await fetch(`${this.SERVER_URL}/api/chat`, {
      method: 'POST',
      headers: { 'Content-Type': 'application/json' },
      body: JSON.stringify({
        model: this.COMPRESSION_MODEL,
        messages,
        stream: false,
        options: {
          temperature: 0.1,
          num_predict: 2000  // 🔧 AUGMENTÉ pour éviter la troncature JSON
        }
      })
    });

    console.log(`📡 Réponse Ollama: ${response.status} ${response.statusText}`);

    if (!response.ok) {
      const errorText = await response.text();
      console.error(`❌ Erreur Ollama ${response.status}:`, errorText);
      throw new Error(`Ollama error: ${response.status} - ${errorText}`);
    }

    const result = await response.json();
    console.log('✅ Réponse Ollama reçue:', result.message?.content?.substring(0, 200) + '...');
    return result.message?.content || '';
  }

  /**
   * 🔧 PARSER LA RÉPONSE AVEC RÉPARATION JSON
   */
  private parseResponse(response: string): SmartCompressionResult {
    try {
      let jsonContent = response.trim();

      // Supprimer les balises <think>
      if (jsonContent.includes('</think>')) {
        jsonContent = jsonContent.split('</think>')[1].trim();
      }

      // Extraire le JSON entre accolades
      const jsonMatch = jsonContent.match(/\{[\s\S]*?\}/);
      if (jsonMatch) {
        jsonContent = jsonMatch[0];
      }

      // 🔧 TENTATIVE 1: Parser directement
      try {
        const parsed = JSON.parse(jsonContent);

        // 🚀 NOUVEAU FORMAT: Gestion intelligente
        if (parsed.rewrittenContext) {
          console.log(`🔍 Validation: Contexte réécrit (${parsed.rewrittenContext.length} caractères)`);
          console.log(`🔍 Modifications: ${parsed.modificationsApportees || 'Non spécifiées'}`);

          return {
            messagesToKeep: [], // Pas de sélection, tout est réécrit
            messagesToSummarize: [], // Pas de résumé, tout est réécrit
            summaryOfRemoved: parsed.rewrittenContext.toString().trim()
          };
        }

        // 🔧 ANCIEN FORMAT: Sélection + résumé (fallback)
        const validKeepIds = Array.isArray(parsed.messagesToKeep)
          ? parsed.messagesToKeep.filter((id: any) => typeof id === 'string' && id.length > 0)
          : [];
        const validSummarizeIds = Array.isArray(parsed.messagesToSummarize)
          ? parsed.messagesToSummarize.filter((id: any) => typeof id === 'string' && id.length > 0)
          : [];

        console.log(`🔍 Validation: ${validKeepIds.length} IDs à garder, ${validSummarizeIds.length} IDs à résumer`);

        return {
          messagesToKeep: validKeepIds,
          messagesToSummarize: validSummarizeIds,
          summaryOfRemoved: (parsed.summaryOfRemoved || '').toString().trim()
        };
      } catch (parseError) {
        console.warn('🔧 JSON mal formé, tentative de réparation...');

        // 🔧 TENTATIVE 2: Réparer le JSON tronqué
        const repairedJson = this.repairTruncatedJson(jsonContent);
        const parsed = JSON.parse(repairedJson);

        console.log('✅ JSON réparé avec succès');

        // 🔍 VALIDATION STRICTE DES IDS (réparation)
        const validKeepIds = Array.isArray(parsed.messagesToKeep)
          ? parsed.messagesToKeep.filter((id: any) => typeof id === 'string' && id.length > 0)
          : [];
        const validSummarizeIds = Array.isArray(parsed.messagesToSummarize)
          ? parsed.messagesToSummarize.filter((id: any) => typeof id === 'string' && id.length > 0)
          : [];

        return {
          messagesToKeep: validKeepIds,
          messagesToSummarize: validSummarizeIds,
          summaryOfRemoved: (parsed.summaryOfRemoved || '').toString().trim()
        };
      }

    } catch (error) {
      console.error('❌ ERREUR PARSING JSON - IMPOSSIBLE À RÉPARER:', error);
      console.error('📄 Réponse LLM brute:', response);
      throw new Error(`JSON parsing failed: ${error instanceof Error ? error.message : String(error)}`);
    }
  }

  /**
   * 🔧 RÉPARER JSON TRONQUÉ
   */
  private repairTruncatedJson(jsonStr: string): string {
    let repaired = jsonStr.trim();

    // Compter les accolades ouvertes/fermées
    const openBraces = (repaired.match(/\{/g) || []).length;
    const closeBraces = (repaired.match(/\}/g) || []).length;

    // Compter les crochets ouverts/fermés
    const openBrackets = (repaired.match(/\[/g) || []).length;
    const closeBrackets = (repaired.match(/\]/g) || []).length;

    // Compter les guillemets
    const quotes = (repaired.match(/"/g) || []).length;

    // Fermer les guillemets si impairs
    if (quotes % 2 !== 0) {
      repaired += '"';
    }

    // Fermer les crochets manquants
    for (let i = 0; i < openBrackets - closeBrackets; i++) {
      repaired += ']';
    }

    // Fermer les accolades manquantes
    for (let i = 0; i < openBraces - closeBraces; i++) {
      repaired += '}';
    }

    console.log('🔧 JSON réparé:', repaired);
    return repaired;
  }

  /**
   * 🏗️ CONSTRUIRE LE RÉSULTAT
   */
  private buildResult(
    ragMessages: ClaraMessage[],
    conversationMessages: ClaraMessage[],
    analysis: SmartCompressionResult
  ): ClaraMessage[] {
    const result: ClaraMessage[] = [];

    // 🚀 RÉÉCRITURE COMPLÈTE: Remplacer TOUS les messages par un seul message ultra-compressé
    if (analysis.messagesToKeep.length === 0 && analysis.messagesToSummarize.length === 0 && analysis.summaryOfRemoved) {
      // Créer UN SEUL message qui remplace TOUT le contexte (conversation + RAG)
      const rewrittenMessage: ClaraMessage = {
        id: `rewritten-context-${Date.now()}`,
        role: 'assistant',
        content: `🧠 **Contexte intelligemment optimisé**:\n\n${analysis.summaryOfRemoved}`,
        timestamp: new Date(),
        metadata: {
          isCompressed: true,
          compressionType: 'intelligent_rewrite',
          originalMessageCount: conversationMessages.length,
          rewrittenFromIds: conversationMessages.map(msg => msg.id),
          compressionRatio: analysis.summaryOfRemoved.length / conversationMessages.reduce((sum, msg) => sum + msg.content.length, 0)
        }
      };

      result.push(rewrittenMessage);
      return result;
    }

    // 🔧 FALLBACK: Sélection + résumé (si la réécriture échoue)
    console.log('⚠️ Fallback vers sélection + résumé');

    // 1. Garder les documents RAG
    result.push(...ragMessages);

    // 2. Messages choisis par le LLM
    const keptMessages = conversationMessages.filter(msg =>
      analysis.messagesToKeep.includes(msg.id)
    );
    result.push(...keptMessages);

    // 3. Résumé si nécessaire
    if (analysis.summaryOfRemoved && analysis.messagesToSummarize.length > 0) {
      const summaryMessage: ClaraMessage = {
        id: `smart-summary-${Date.now()}`,
        role: 'assistant',
        content: `📋 **Résumé intelligent** (${analysis.messagesToSummarize.length} messages):\n\n${analysis.summaryOfRemoved}`,
        timestamp: new Date(),
        metadata: {
          isCompressed: true,
          compressionType: 'smart',
          removedMessageIds: analysis.messagesToSummarize
        }
      };
      result.push(summaryMessage);
    }

    // 4. Trier chronologiquement
    result.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

    return result;
  }
}

// Export singleton
export const perfectCompressor = new PerfectCompressor();
