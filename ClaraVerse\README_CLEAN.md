# 🚀 WeMa IA - Assistant IA Intelligent

**WeMa IA** est un assistant IA moderne et performant avec support OCR, RAG et streaming optimisé.

## ✨ Fonctionnalités

### 🤖 **Assistant IA Avancé**
- Support multi-modèles (LM Studio, Ollama, OpenAI, OpenRouter)
- Streaming optimisé avec debouncing intelligent (68.9% réduction updates UI)
- Mode autonome avec outils intégrés
- Interface utilisateur moderne et responsive

### 📄 **Traitement de Documents**
- OCR premium avec Tesseract
- Support PDF, images, CSV, texte
- Préservation de la mise en page
- Anonymisation GDPR

### 🔍 **RAG (Retrieval-Augmented Generation)**
- Système RAG hybride intelligent
- LightRAG + Qdrant + BGE-M3
- Recherche sémantique avancée
- Gestion de documents volumineux

### 📊 **Monitoring & Performance**
- Intégration Langfuse pour le monitoring
- Métriques de performance en temps réel
- Tests de performance automatisés

## 🚀 Installation Rapide

### Prérequis
- Node.js 18+
- Python 3.9+
- LM Studio ou Ollama (optionnel)

### 1. Installation Frontend
```bash
cd ClaraVerse
npm install
npm run dev
```

### 2. Installation Backend
```bash
cd py_backend
pip install -r requirements.txt
python main.py
```

### 3. Accès
- Frontend: http://localhost:5173
- Backend: http://localhost:5001

## 📈 Performances Optimisées

### Résultats des Tests
- **Premier token**: 3.02s ✅ Excellent
- **Vitesse**: 15.2 mots/seconde ✅ Excellent  
- **Réduction UI**: 68.9% ✅ Optimisé
- **Streaming**: 499 chunks → 93 updates UI

## 🛠️ Configuration

### LM Studio (Recommandé)
1. Installer LM Studio
2. Charger un modèle (ex: Qwen 3-4B)
3. Activer CORS dans les paramètres
4. Démarrer le serveur sur localhost:1234

### RAG System
Le système RAG est automatiquement configuré avec :
- **LightRAG** pour la recherche intelligente
- **Qdrant** pour le stockage vectoriel
- **BGE-M3** pour les embeddings

## 📁 Structure Propre

```
ClaraVerse/
├── src/                    # Frontend React + TypeScript
│   ├── components/         # Composants UI optimisés
│   ├── services/          # Services API
│   ├── stores/            # État global (Zustand)
│   └── utils/             # Utilitaires + StreamOptimizer
├── py_backend/            # Backend Python FastAPI
│   ├── main.py           # Serveur principal
│   ├── rag_premium_service.py  # Service RAG
│   ├── ocr_service.py    # Service OCR
│   └── langfuse_service.py     # Monitoring
└── docs/                  # Documentation
```

## 🧪 Tests Disponibles

### Tests de Performance
```bash
cd py_backend
python test_streaming_optimized.py  # Test streaming optimisé
python test_optimizations.py        # Test général
python test_non_streaming.py        # Comparaison streaming/non-streaming
```

## 🔧 Scripts Utiles

### Frontend
- `npm run dev` - Développement avec hot reload
- `npm run build` - Build production optimisé
- `npm run preview` - Aperçu production

### Backend
- `python main.py` - Serveur principal
- Tests spécifiques disponibles dans py_backend/

## 📚 Documentation

- [Guide de démarrage rapide](docs/QUICK_START_GUIDE.md)
- [Documentation Clara Assistant](docs/clara_assistant_documentation.md)
- [Configuration Chat](docs/clara_chat_settings_guide.md)

## 🎯 Optimisations Appliquées

### Frontend
- ✅ Debouncing intelligent des chunks streaming
- ✅ Réduction des re-renders React
- ✅ Buffer optimisé (100ms/100 chars)
- ✅ Gestion mémoire améliorée

### Backend
- ✅ Communication directe LM Studio (CORS)
- ✅ Suppression des proxies inutiles
- ✅ Optimisation des endpoints
- ✅ Monitoring Langfuse intégré

## 📄 Licence

MIT License - voir [LICENSE](LICENSE) pour plus de détails.

---

**WeMa IA** - Assistant IA optimisé et performant 🚀
