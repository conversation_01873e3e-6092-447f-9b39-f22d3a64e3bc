@echo off
echo 🧪 Configuration de test WeMa IA
echo ================================

echo 📋 Étape 1: Configuration des fichiers
copy .env.test .env
echo ✅ Configuration de test activée

echo.
echo 📋 Étape 2: Vérification des dépendances
echo 🔍 Vérification Python...
python --version
if errorlevel 1 (
    echo ❌ Python non trouvé
    pause
    exit /b 1
)

echo 🔍 Vérification Node.js...
node --version
if errorlevel 1 (
    echo ❌ Node.js non trouvé
    pause
    exit /b 1
)

echo.
echo 📋 Étape 3: Installation des dépendances
echo 📦 Installation dépendances Python...
cd py_backend
pip install -r requirements.txt
cd ..

echo 📦 Installation dépendances Node.js...
npm install

echo.
echo 📋 Étape 4: Création des dossiers
mkdir data 2>nul
mkdir server-inference 2>nul
mkdir admin-system 2>nul

echo.
echo 🎯 CONFIGURATION DE TEST PRÊTE !
echo ================================
echo.
echo 🚀 Pour tester:
echo.
echo 1. SERVEUR D'INFÉRENCE (Terminal 1):
echo    cd server-inference
echo    python start_inference_server.py
echo.
echo 2. BACKEND (Terminal 2):
echo    cd py_backend
echo    python main.py
echo.
echo 3. FRONTEND (Terminal 3):
echo    npm run dev
echo.
echo 4. TESTS:
echo    - Mode utilisateur: http://localhost:3000
echo    - Mode admin: http://localhost:3000?admin=true
echo.
echo 📝 Notes:
echo - Serveur d'inférence: localhost:1235
echo - Backend: localhost:5001
echo - Frontend: localhost:3000
echo - Fallback local activé si serveur indisponible
echo.
pause
