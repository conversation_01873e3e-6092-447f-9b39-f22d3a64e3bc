/**
 * 🧠 Context Manager - Gestion intelligente du contexte
 * 
 * Responsabilités :
 * - Optimisation du contexte pour les limites des modèles
 * - Gestion de l'historique de conversation
 * - Compression intelligente du contenu
 * - Priorisation des messages importants
 * - Gestion de la mémoire conversationnelle
 */

import type { ClaraMessage } from '../../../types/clara_assistant_types';
import { perfectCompressor, type CompressionConfig } from './PerfectCompressor';

export interface ContextOptimizationResult {
  optimizedHistory: ClaraMessage[];
  optimizedRagContext: string;
  contextStats: {
    originalChars: number;
    optimizedChars: number;
    compressionRatio: number;
    strategy: string;
    totalChars: number;
  };
}

export interface ContextLimits {
  maxTokens: number;
  maxChars: number;
  charsPerToken: number;
  reserveTokens: number; // Tokens réservés pour la réponse
}

export class ContextManager {
  private readonly DEFAULT_LIMITS: ContextLimits = {
    maxTokens: 32000,
    maxChars: 128000,
    charsPerToken: 4,
    reserveTokens: 4000
  };

  /**
   * 🧠 Optimiser le contexte avec compression IA intelligente
   */
  public async optimizeContextWithAI(
    conversationHistory: ClaraMessage[],
    ragContext: string,
    currentMessage: string,
    limits: Partial<ContextLimits> = {}
  ): Promise<ContextOptimizationResult> {
    const contextLimits = { ...this.DEFAULT_LIMITS, ...limits };
    const maxContextChars = (contextLimits.maxTokens - contextLimits.reserveTokens) * contextLimits.charsPerToken;

    console.log(`🧠 Optimisation contexte IA: limite ${maxContextChars} chars`);

    // Calculer la taille actuelle
    const currentMessageChars = currentMessage.length;
    const ragContextChars = ragContext.length;
    const historyChars = conversationHistory.reduce((sum, msg) => sum + msg.content.length, 0);
    const totalChars = currentMessageChars + ragContextChars + historyChars;

    console.log(`📊 Taille actuelle: ${totalChars} chars`);

    // Analyser si compression IA nécessaire
    const shouldCompress = perfectCompressor.shouldCompress(conversationHistory);

    if (shouldCompress) {
      console.log(`🤖 Compression IA nécessaire: ${totalChars} chars`);

      try {
        const compressionConfig: CompressionConfig = {
          compression_threshold: 20000,
          max_tokens: 35000,
          target_tokens: Math.floor(contextLimits.maxTokens * 0.4), // 40% du contexte max
          compression_model: "qwen3-14b-optimized"
        };

        const optimizedHistory = await perfectCompressor.compress(
          conversationHistory,
          compressionConfig
        );

        const optimizedHistoryChars = optimizedHistory.reduce((sum, msg) => sum + msg.content.length, 0);
        const finalTotalChars = currentMessageChars + ragContextChars + optimizedHistoryChars;

        return {
          optimizedHistory,
          optimizedRagContext: ragContext,
          contextStats: {
            originalChars: totalChars,
            optimizedChars: finalTotalChars,
            compressionRatio: finalTotalChars / totalChars,
            strategy: 'perfect_compression',
            totalChars: finalTotalChars
          }
        };
      } catch (error) {
        console.warn('⚠️ Erreur compression IA, fallback vers optimisation classique:', error);
        return this.optimizeContext(conversationHistory, ragContext, currentMessage, limits);
      }
    }

    // Pas de compression nécessaire, utiliser l'optimisation classique
    return this.optimizeContext(conversationHistory, ragContext, currentMessage, limits);
  }

  /**
   * Optimiser le contexte pour rester dans les limites du modèle (méthode classique)
   */
  public optimizeContext(
    conversationHistory: ClaraMessage[],
    ragContext: string,
    currentMessage: string,
    limits: Partial<ContextLimits> = {}
  ): ContextOptimizationResult {
    const contextLimits = { ...this.DEFAULT_LIMITS, ...limits };
    const maxContextChars = (contextLimits.maxTokens - contextLimits.reserveTokens) * contextLimits.charsPerToken;

    console.log(`🧠 Optimisation contexte: limite ${maxContextChars} chars`);

    // Calculer la taille actuelle
    const currentMessageChars = currentMessage.length;
    const ragContextChars = ragContext.length;
    const historyChars = conversationHistory.reduce((sum, msg) => sum + msg.content.length, 0);
    const totalChars = currentMessageChars + ragContextChars + historyChars;

    console.log(`📊 Taille actuelle: ${totalChars} chars (message: ${currentMessageChars}, RAG: ${ragContextChars}, historique: ${historyChars})`);

    // Si on est dans les limites, pas d'optimisation nécessaire
    if (totalChars <= maxContextChars) {
      return {
        optimizedHistory: conversationHistory,
        optimizedRagContext: ragContext,
        contextStats: {
          originalChars: totalChars,
          optimizedChars: totalChars,
          compressionRatio: 1.0,
          strategy: 'no_optimization',
          totalChars
        }
      };
    }

    // Calculer l'espace disponible pour l'historique
    const availableForHistory = maxContextChars - currentMessageChars - ragContextChars;
    
    if (availableForHistory <= 0) {
      // Cas extrême : même sans historique, on dépasse
      console.warn('⚠️ Contexte trop volumineux même sans historique');
      return this.handleExtremeCase(conversationHistory, ragContext, maxContextChars);
    }

    // Optimiser l'historique
    const optimizedHistory = this.optimizeHistory(conversationHistory, availableForHistory);
    const optimizedHistoryChars = optimizedHistory.reduce((sum, msg) => sum + msg.content.length, 0);
    const finalTotalChars = currentMessageChars + ragContextChars + optimizedHistoryChars;

    return {
      optimizedHistory,
      optimizedRagContext: ragContext,
      contextStats: {
        originalChars: totalChars,
        optimizedChars: finalTotalChars,
        compressionRatio: finalTotalChars / totalChars,
        strategy: 'history_optimization',
        totalChars: finalTotalChars
      }
    };
  }

  /**
   * Résumer une conversation longue
   */
  public summarizeConversation(
    messages: ClaraMessage[],
    maxSummaryLength: number = 500
  ): string {
    if (messages.length === 0) {
      return '';
    }

    // Extraire les points clés de la conversation
    const userQueries: string[] = [];
    const assistantResponses: string[] = [];
    const toolsUsed = new Set<string>();

    for (const message of messages) {
      if (message.role === 'user') {
        const query = message.content.length > 100 
          ? message.content.substring(0, 100) + '...' 
          : message.content;
        userQueries.push(query);
      } else if (message.role === 'assistant') {
        // Extraire les points clés de la réponse
        const keyPoints = this.extractKeyPoints(message.content);
        assistantResponses.push(keyPoints);
        
        // Collecter les outils utilisés
        if (message.metadata?.toolsUsed) {
          message.metadata.toolsUsed.forEach(tool => toolsUsed.add(tool));
        }
      }
    }

    // Construire le résumé
    let summary = `📋 **Résumé de conversation (${messages.length} messages):**\n\n`;
    
    if (userQueries.length > 0) {
      summary += `**Questions principales:**\n`;
      userQueries.slice(-3).forEach((query, i) => {
        summary += `${i + 1}. ${query}\n`;
      });
      summary += '\n';
    }

    if (assistantResponses.length > 0) {
      summary += `**Points clés des réponses:**\n`;
      assistantResponses.slice(-2).forEach((response, i) => {
        summary += `• ${response}\n`;
      });
      summary += '\n';
    }

    if (toolsUsed.size > 0) {
      summary += `**Outils utilisés:** ${Array.from(toolsUsed).join(', ')}\n\n`;
    }

    // Tronquer si nécessaire
    if (summary.length > maxSummaryLength) {
      summary = summary.substring(0, maxSummaryLength - 3) + '...';
    }

    return summary;
  }

  /**
   * Analyser l'importance d'un message
   */
  public analyzeMessageImportance(message: ClaraMessage): number {
    let score = 0.5; // Score de base

    // Messages récents sont plus importants
    const ageInMinutes = (Date.now() - message.timestamp.getTime()) / (1000 * 60);
    const recencyScore = Math.max(0, 1 - (ageInMinutes / 60)); // Décroissance sur 1 heure
    score += recencyScore * 0.3;

    // Messages avec outils sont plus importants
    if (message.metadata?.toolsUsed && message.metadata.toolsUsed.length > 0) {
      score += 0.2;
    }

    // Messages longs peuvent être plus informatifs
    const lengthScore = Math.min(1, message.content.length / 1000);
    score += lengthScore * 0.1;

    // Messages d'erreur sont moins importants
    if (message.content.toLowerCase().includes('erreur') || 
        message.content.toLowerCase().includes('error')) {
      score -= 0.2;
    }

    // Messages de l'utilisateur sont généralement importants
    if (message.role === 'user') {
      score += 0.2;
    }

    return Math.max(0, Math.min(1, score));
  }

  /**
   * Nettoyer et optimiser le contenu d'un message
   */
  public cleanMessageContent(content: string, maxLength?: number): string {
    let cleaned = content;

    // Supprimer les répétitions excessives
    cleaned = cleaned.replace(/(.{10,}?)\1{2,}/g, '$1[...répété]');

    // Supprimer les espaces multiples
    cleaned = cleaned.replace(/\s+/g, ' ');

    // Supprimer les lignes vides multiples
    cleaned = cleaned.replace(/\n\s*\n\s*\n/g, '\n\n');

    // Tronquer si nécessaire
    if (maxLength && cleaned.length > maxLength) {
      cleaned = cleaned.substring(0, maxLength - 3) + '...';
    }

    return cleaned.trim();
  }

  // ============================================================================
  // MÉTHODES PRIVÉES
  // ============================================================================

  /**
   * Optimiser l'historique de conversation
   */
  private optimizeHistory(
    history: ClaraMessage[],
    maxChars: number
  ): ClaraMessage[] {
    if (history.length === 0) {
      return [];
    }

    // Stratégie 1: Garder les messages les plus récents et importants
    const scoredMessages = history.map(msg => ({
      message: msg,
      importance: this.analyzeMessageImportance(msg)
    }));

    // Trier par importance décroissante
    scoredMessages.sort((a, b) => b.importance - a.importance);

    // Sélectionner les messages jusqu'à la limite
    const selectedMessages: ClaraMessage[] = [];
    let currentChars = 0;

    for (const { message } of scoredMessages) {
      const messageChars = message.content.length;
      
      if (currentChars + messageChars <= maxChars) {
        selectedMessages.push(message);
        currentChars += messageChars;
      } else {
        // Essayer de compresser le message
        const maxRemainingChars = maxChars - currentChars;
        if (maxRemainingChars > 100) { // Minimum viable
          const compressedContent = this.cleanMessageContent(message.content, maxRemainingChars);
          selectedMessages.push({
            ...message,
            content: compressedContent
          });
          break;
        }
      }
    }

    // Remettre dans l'ordre chronologique
    selectedMessages.sort((a, b) => a.timestamp.getTime() - b.timestamp.getTime());

    console.log(`🧠 Historique optimisé: ${history.length} → ${selectedMessages.length} messages`);
    return selectedMessages;
  }

  /**
   * Gérer les cas extrêmes où même sans historique on dépasse
   */
  private handleExtremeCase(
    history: ClaraMessage[],
    ragContext: string,
    maxChars: number
  ): ContextOptimizationResult {
    console.warn('⚠️ Cas extrême: compression drastique nécessaire');

    // Essayer de compresser le contexte RAG
    const compressedRag = ragContext.length > maxChars / 2 
      ? ragContext.substring(0, Math.floor(maxChars / 2)) + '...[contexte tronqué]'
      : ragContext;

    // Garder seulement un résumé de l'historique
    const summary = this.summarizeConversation(history, Math.floor(maxChars / 4));

    return {
      optimizedHistory: summary ? [{
        id: 'summary',
        role: 'assistant',
        content: summary,
        timestamp: new Date()
      }] : [],
      optimizedRagContext: compressedRag,
      contextStats: {
        originalChars: ragContext.length + history.reduce((sum, msg) => sum + msg.content.length, 0),
        optimizedChars: compressedRag.length + summary.length,
        compressionRatio: 0.1,
        strategy: 'extreme_compression',
        totalChars: compressedRag.length + summary.length
      }
    };
  }

  /**
   * Extraire les points clés d'un texte
   */
  private extractKeyPoints(text: string, maxLength: number = 150): string {
    // Simplification : prendre le début et chercher des phrases importantes
    const sentences = text.split(/[.!?]+/).filter(s => s.trim().length > 10);
    
    if (sentences.length === 0) {
      return text.substring(0, maxLength);
    }

    // Prendre les premières phrases jusqu'à la limite
    let result = '';
    for (const sentence of sentences) {
      if (result.length + sentence.length + 2 <= maxLength) {
        result += (result ? '. ' : '') + sentence.trim();
      } else {
        break;
      }
    }

    return result || text.substring(0, maxLength);
  }
}

// Export singleton
export const contextManager = new ContextManager();
