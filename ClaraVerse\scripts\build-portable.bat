@echo off
echo 📦 Création application portable WeMa IA
echo ==========================================

REM Créer le dossier portable
if exist "WeMa-IA-Portable" rmdir /s /q "WeMa-IA-Portable"
mkdir "WeMa-IA-Portable"

echo 📋 Étape 1: Build React
call npm run build
if errorlevel 1 (
    echo ❌ Erreur build React
    pause
    exit /b 1
)

echo 📋 Étape 2: Copie des fichiers
xcopy /E /I "build" "WeMa-IA-Portable\build"
xcopy /E /I "py_backend" "WeMa-IA-Portable\py_backend"
copy ".env.production" "WeMa-IA-Portable\.env"

echo 📋 Étape 3: Création du lanceur
echo @echo off > "WeMa-IA-Portable\start-wema-ia.bat"
echo echo 🚀 Démarrage WeMa IA >> "WeMa-IA-Portable\start-wema-ia.bat"
echo echo ================== >> "WeMa-IA-Portable\start-wema-ia.bat"
echo. >> "WeMa-IA-Portable\start-wema-ia.bat"
echo REM Vérifier Python >> "WeMa-IA-Portable\start-wema-ia.bat"
echo python --version ^>nul 2^>^&1 >> "WeMa-IA-Portable\start-wema-ia.bat"
echo if errorlevel 1 ^( >> "WeMa-IA-Portable\start-wema-ia.bat"
echo     echo ❌ Python non trouvé >> "WeMa-IA-Portable\start-wema-ia.bat"
echo     echo 📦 Installez Python depuis python.org >> "WeMa-IA-Portable\start-wema-ia.bat"
echo     pause >> "WeMa-IA-Portable\start-wema-ia.bat"
echo     exit /b 1 >> "WeMa-IA-Portable\start-wema-ia.bat"
echo ^) >> "WeMa-IA-Portable\start-wema-ia.bat"
echo. >> "WeMa-IA-Portable\start-wema-ia.bat"
echo echo 📦 Installation dépendances... >> "WeMa-IA-Portable\start-wema-ia.bat"
echo cd py_backend >> "WeMa-IA-Portable\start-wema-ia.bat"
echo pip install -r requirements.txt >> "WeMa-IA-Portable\start-wema-ia.bat"
echo. >> "WeMa-IA-Portable\start-wema-ia.bat"
echo echo 🚀 Démarrage backend... >> "WeMa-IA-Portable\start-wema-ia.bat"
echo start /min python main.py >> "WeMa-IA-Portable\start-wema-ia.bat"
echo. >> "WeMa-IA-Portable\start-wema-ia.bat"
echo echo ⏳ Attente démarrage... >> "WeMa-IA-Portable\start-wema-ia.bat"
echo timeout /t 5 /nobreak ^>nul >> "WeMa-IA-Portable\start-wema-ia.bat"
echo. >> "WeMa-IA-Portable\start-wema-ia.bat"
echo echo 🌐 Ouverture interface... >> "WeMa-IA-Portable\start-wema-ia.bat"
echo start http://localhost:5001 >> "WeMa-IA-Portable\start-wema-ia.bat"
echo. >> "WeMa-IA-Portable\start-wema-ia.bat"
echo echo ✅ WeMa IA démarré ! >> "WeMa-IA-Portable\start-wema-ia.bat"
echo echo 🛑 Fermez cette fenêtre pour arrêter >> "WeMa-IA-Portable\start-wema-ia.bat"
echo pause >> "WeMa-IA-Portable\start-wema-ia.bat"

echo 📋 Étape 4: Création du README
echo # WeMa IA - Application Portable > "WeMa-IA-Portable\README.txt"
echo. >> "WeMa-IA-Portable\README.txt"
echo ## Installation: >> "WeMa-IA-Portable\README.txt"
echo 1. Assurez-vous que Python est installé >> "WeMa-IA-Portable\README.txt"
echo 2. Double-cliquez sur start-wema-ia.bat >> "WeMa-IA-Portable\README.txt"
echo 3. L'interface s'ouvre automatiquement >> "WeMa-IA-Portable\README.txt"
echo. >> "WeMa-IA-Portable\README.txt"
echo ## Taille: ~20MB (sans Python) >> "WeMa-IA-Portable\README.txt"
echo ## Prérequis: Python 3.8+ >> "WeMa-IA-Portable\README.txt"

echo.
echo ✅ Application portable créée !
echo 📁 Dossier: WeMa-IA-Portable\
echo 📊 Taille: ~20MB (vs 80-120MB Electron)
echo 🚀 Lancement: start-wema-ia.bat
echo.
pause
