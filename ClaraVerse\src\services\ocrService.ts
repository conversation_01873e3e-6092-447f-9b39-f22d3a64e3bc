/**
 * 🚀 SERVICE OCR OPTIMISÉ POUR WEMA IA
 * 
 * Service OCR avancé avec prétraitement intelligent,
 * détection de tableaux, et correction d'erreurs automatique
 */

export interface OCROptions {
  language?: string;
  preserveLayout?: boolean;
  detectTables?: boolean;
  enhanceContrast?: boolean;
  correctSkew?: boolean;
  dpi?: number;
}

export interface OCRResult {
  success: boolean;
  text: string;
  confidence: number;
  qualityScore: number;
  qualityText: string;
  processingTime: number;
  detectedLanguage: string;
  tablesDetected: number;
  wordsCount: number;
  error?: string;
  metadata?: {
    method: string;
    pages: number;
    fileSize: number;
    originalDimensions?: { width: number; height: number };
    processedDimensions?: { width: number; height: number };
  };
}

export interface OCRProgress {
  stage: 'uploading' | 'preprocessing' | 'ocr' | 'postprocessing' | 'complete' | 'error';
  progress: number;
  message: string;
  details?: string;
}

export class OCRService {
  private readonly baseUrl: string;
  private readonly defaultOptions: OCROptions = {
    language: 'fra+eng',
    preserveLayout: true,
    detectTables: true,
    enhanceContrast: true,
    correctSkew: true,
    dpi: 300
  };

  constructor(baseUrl: string = 'http://localhost:5001') {
    this.baseUrl = baseUrl;
  }

  /**
   * 🔍 Traiter un document avec OCR optimisé
   */
  async processDocument(
    file: File, 
    options: Partial<OCROptions> = {},
    onProgress?: (progress: OCRProgress) => void
  ): Promise<OCRResult> {
    const startTime = Date.now();
    const finalOptions = { ...this.defaultOptions, ...options };

    try {
      // 📤 ÉTAPE 1: Upload et validation
      onProgress?.({
        stage: 'uploading',
        progress: 10,
        message: 'Validation et upload du fichier...',
        details: `Fichier: ${file.name} (${this.formatFileSize(file.size)})`
      });

      const validationResult = this.validateFile(file);
      if (!validationResult.valid) {
        throw new Error(validationResult.error);
      }

      // 🔄 ÉTAPE 2: Prétraitement
      onProgress?.({
        stage: 'preprocessing',
        progress: 30,
        message: 'Prétraitement intelligent...',
        details: 'Optimisation de la qualité d\'image'
      });

      const fileBase64 = await this.fileToBase64(file);

      // 🤖 ÉTAPE 3: OCR Processing
      onProgress?.({
        stage: 'ocr',
        progress: 60,
        message: 'Extraction de texte en cours...',
        details: `Langue: ${finalOptions.language}, DPI: ${finalOptions.dpi}`
      });

      const ocrResult = await this.callOCRAPI(fileBase64, file, finalOptions);

      // 🔧 ÉTAPE 4: Post-traitement
      onProgress?.({
        stage: 'postprocessing',
        progress: 90,
        message: 'Nettoyage et optimisation...',
        details: 'Correction des erreurs OCR'
      });

      const processedResult = this.postProcessResult(ocrResult, startTime, file);

      // ✅ ÉTAPE 5: Terminé
      onProgress?.({
        stage: 'complete',
        progress: 100,
        message: 'Traitement terminé avec succès !',
        details: `Qualité: ${processedResult.qualityText} (${processedResult.qualityScore}%)`
      });

      return processedResult;

    } catch (error) {
      const errorMessage = error instanceof Error ? error.message : 'Erreur inconnue';
      
      onProgress?.({
        stage: 'error',
        progress: 0,
        message: 'Erreur de traitement',
        details: errorMessage
      });

      return {
        success: false,
        text: '',
        confidence: 0,
        qualityScore: 0,
        qualityText: 'Échec',
        processingTime: Date.now() - startTime,
        detectedLanguage: 'unknown',
        tablesDetected: 0,
        wordsCount: 0,
        error: errorMessage
      };
    }
  }

  /**
   * 🔍 Valider le fichier avant traitement
   */
  private validateFile(file: File): { valid: boolean; error?: string } {
    const maxSize = 50 * 1024 * 1024; // 50MB
    const supportedTypes = [
      'image/jpeg', 'image/jpg', 'image/png', 'image/tiff', 'image/bmp',
      'application/pdf',
      'text/plain',
      'application/vnd.openxmlformats-officedocument.wordprocessingml.document',
      'application/msword'
    ];

    if (file.size > maxSize) {
      return { valid: false, error: `Fichier trop volumineux (max ${this.formatFileSize(maxSize)})` };
    }

    if (!supportedTypes.includes(file.type)) {
      return { valid: false, error: `Type de fichier non supporté: ${file.type}` };
    }

    return { valid: true };
  }

  /**
   * 📡 Appeler l'API OCR backend
   */
  private async callOCRAPI(
    fileBase64: string, 
    file: File, 
    options: OCROptions
  ): Promise<any> {
    const response = await fetch(`${this.baseUrl}/ocr/process`, {
      method: 'POST',
      headers: {
        'Content-Type': 'application/json',
      },
      body: JSON.stringify({
        file_base64: fileBase64,
        file_type: file.name.split('.').pop()?.toLowerCase() || '',
        language: options.language,
        preserve_layout: options.preserveLayout,
        detect_tables: options.detectTables,
        enhance_contrast: options.enhanceContrast,
        correct_skew: options.correctSkew,
        dpi: options.dpi
      })
    });

    if (!response.ok) {
      throw new Error(`Erreur API OCR: ${response.status} ${response.statusText}`);
    }

    const result = await response.json();
    
    if (!result.success) {
      throw new Error(result.error || 'Erreur de traitement OCR');
    }

    return result.data;
  }

  /**
   * 🔧 Post-traitement du résultat OCR
   */
  private postProcessResult(ocrResult: any, startTime: number, file: File): OCRResult {
    const processingTime = Date.now() - startTime;
    const text = ocrResult.text || '';
    const confidence = ocrResult.confidence || 0;
    
    // Calculer le score de qualité
    const qualityScore = this.calculateQualityScore(ocrResult, text, processingTime);
    const qualityText = this.getQualityText(qualityScore);

    return {
      success: true,
      text,
      confidence,
      qualityScore,
      qualityText,
      processingTime,
      detectedLanguage: ocrResult.detected_language || 'unknown',
      tablesDetected: ocrResult.tables_detected || 0,
      wordsCount: text.split(/\s+/).filter(word => word.length > 0).length,
      metadata: {
        method: ocrResult.method_used || 'unknown',
        pages: ocrResult.pages_count || 1,
        fileSize: file.size,
        originalDimensions: ocrResult.original_dimensions,
        processedDimensions: ocrResult.processed_dimensions
      }
    };
  }

  /**
   * 📊 Calculer le score de qualité
   */
  private calculateQualityScore(ocrResult: any, text: string, processingTime: number): number {
    let score = 0;

    // Confiance OCR (40%)
    score += (ocrResult.confidence || 0) * 0.4;

    // Longueur du texte (30%)
    const textLength = text.length;
    if (textLength > 0) {
      const lengthScore = Math.min(30, (textLength / 1000) * 30);
      score += lengthScore;
    }

    // Structures détectées (20%)
    if (ocrResult.tables_detected > 0) score += 15;
    if (ocrResult.detected_language !== 'unknown') score += 5;

    // Performance (10%)
    if (processingTime < 30000) score += 10;
    else if (processingTime < 60000) score += 5;

    return Math.min(100, Math.round(score));
  }

  /**
   * 📝 Obtenir le texte de qualité
   */
  private getQualityText(score: number): string {
    if (score >= 80) return 'Excellente';
    if (score >= 60) return 'Bonne';
    if (score >= 40) return 'Moyenne';
    return 'Faible';
  }

  /**
   * 🔧 Convertir fichier en base64
   */
  private fileToBase64(file: File): Promise<string> {
    return new Promise((resolve, reject) => {
      const reader = new FileReader();
      reader.onload = () => {
        const result = reader.result as string;
        const base64 = result.split(',')[1];
        resolve(base64);
      };
      reader.onerror = reject;
      reader.readAsDataURL(file);
    });
  }

  /**
   * 📏 Formater la taille de fichier
   */
  private formatFileSize(bytes: number): string {
    if (bytes === 0) return '0 B';
    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));
    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }
}

// Export singleton
export const ocrService = new OCRService();
